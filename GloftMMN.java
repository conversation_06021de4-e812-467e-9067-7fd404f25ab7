import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Hashtable;
import java.util.Random;
import javax.microedition.lcdui.Display;
import javax.microedition.media.Manager;
import javax.microedition.media.Player;
import javax.microedition.midlet.MIDlet;

public final class GloftMMN extends MIDlet {
   public static String a = "";
   static GloftMMN b;
   static h c = null;
   static Display d = null;
   static int e;
   private static byte[][] p;
   private static int[] q;
   private static Hashtable r;
   private static Player[] s = new Player[1];
   private static int[] t;
   private static int[] u;
   private static int[] v;
   private static int[] w;
   private static int[] x;
   private static int[] y;
   static boolean f = false;
   private static byte[] z;
   static InputStream g;
   private static String A;
   static int h;
   private static int[] B;
   private static int C;
   static int[] i;
   private static int D;
   static int j;
   static final String[] k = new String[]{"?", "audio/x-wav", "audio/midi"};
   static Random l;
   static int m;
   static byte[] n;
   static int[] o;

   static void a(String var0) {
      System.gc();
      System.out.println(var0 + " => " + Runtime.getRuntime().freeMemory() / 1024L);
   }

   public GloftMMN() {
      b = this;
      if (c == null) {
         c = new h();
         (new Thread(c)).start();
      }

   }

   public final void startApp() {
      if (d == null) {
         d = Display.getDisplay(this);
      }

      d.setCurrent(c);
      if (c != null) {
         c.showNotify();
      }

   }

   public final void pauseApp() {
      c.hideNotify();
   }

   public final void destroyApp(boolean var1) {
      if (c != null) {
         h.a();
      }

      this.notifyDestroyed();
   }

   static void a() {
      l(32);
   }

   static void a(int var0) {
      m(var0);
   }

   static void b(int var0) {
      n(var0);
   }

   static void c(int var0) {
      a(var0, 1);
   }

   static void a(int var0, int var1) {
      if (f) {
         b();
         a(0, var0, var1, 100, 0);
      }

   }

   static void b() {
      e();
      u(0);
   }

   private static void l(int var0) {
      if (s != null) {
         s = null;
      }

      s = new Player[1];
      if (t != null) {
         t = null;
      }

      t = new int[1];
      if (u != null) {
         u = null;
      }

      u = new int[1];
      if (v != null) {
         v = null;
      }

      v = new int[1];
      if (w != null) {
         w = null;
      }

      w = new int[1];
      if (x != null) {
         x = null;
      }

      x = new int[1];
      if (y != null) {
         y = null;
      }

      y = new int[1];
      e = var0;
      r = new Hashtable(e);
      if (p != null) {
         p = (byte[][])null;
      }

      p = new byte[e][];
      if (q != null) {
         q = null;
      }

      q = new int[e];
   }

   static void c() {
      e();

      int var0;
      for(var0 = 0; var0 < e; ++var0) {
         p[var0] = null;
      }

      p = (byte[][])null;
      q = null;
      r = null;

      for(var0 = 0; var0 < 1; ++var0) {
         u(var0);
      }

      s = null;
      t = null;
      u = null;
      v = null;
      w = null;
      x = null;
      y = null;
      System.gc();
   }

   private static void m(int var0) {
      int var1 = 0;
      if (var0 >= 0) {
         while(p[var1] != null) {
            ++var1;
         }

         p[var1] = j(var0);
         q[var1] = j;
         r.put(new Integer(var1), new Integer(var1));
      }
   }

   private static void n(int var0) {
      if (var0 >= 0) {
         Integer var1 = new Integer(var0);
         var0 = o(var0);
         r.remove(var1);
         p[var0] = null;
         System.gc();
      }
   }

   private static int o(int var0) {
      Integer var1 = new Integer(var0);
      return (Integer)r.get(var1);
   }

   private static void a(int var0, int var1, int var2, int var3, int var4) {
      if (var1 >= 0) {
         try {
            var1 = o(var1);
         } catch (Exception var6) {
            return;
         }

         boolean var5;
         if (!(var5 = u[var0] == var1 && w[var0] == 1)) {
            if (w[var0] == 2 && (v[var0] < var4 || u[var0] == var1)) {
               return;
            }

            u(var0);
         }

         t[var0] = 2;
         u[var0] = var1;
         v[var0] = var4;
         y[var0] = var2;
         x[var0] = var3;
         if (!var5) {
            w[var0] = 0;
         }

      }
   }

   static void d(int var0) {
      t[var0] = 3;
   }

   private static void p(int var0) {
      try {
         int var1 = u[var0];
         s[var0] = Manager.createPlayer(new ByteArrayInputStream(p[var1]), k[q[var1]]);
         if (s[var0] != null) {
            s[var0].realize();
            s[var0].prefetch();
            w[var0] = 1;
         }
      } catch (Exception var2) {
      }
   }

   private static void q(int var0) {
      try {
         int var1 = u[var0];
         if (u[var0] != var1 || w[var0] != 1) {
            p(var0);
         }

         if (y[var0] == 0) {
            s[var0].setLoopCount(-1);
         } else {
            s[var0].setLoopCount(y[var0]);
         }

         s[var0].start();
         w[var0] = 2;
      } catch (Exception var2) {
      }
   }

   private static void r(int var0) {
      if (s[var0] != null) {
         try {
            s[var0].stop();
         } catch (Exception var2) {
         }

         w[var0] = 1;
      }
   }

   private static void s(int var0) {
      try {
         s[var0].stop();
      } catch (Exception var2) {
      }

      w[var0] = 3;
   }

   private static void t(int var0) {
      try {
         s[var0].start();
         w[var0] = 2;
      } catch (Exception var2) {
      }
   }

   static void d() {
      if (t != null) {
         for(int var0 = 0; var0 < 1; ++var0) {
            try {
               if (t[var0] != 0) {
                  switch(t[var0]) {
                  case 1:
                     p(var0);
                     break;
                  case 2:
                     q(var0);
                     break;
                  case 3:
                     r(var0);
                     break;
                  case 4:
                     s(var0);
                     break;
                  case 5:
                     t(var0);
                  }

                  t[var0] = 0;
               }
            } catch (Exception var2) {
            }
         }

      }
   }

   static boolean b(int var0, int var1) {
      try {
         if (var1 < 0) {
            return false;
         }

         if (w[var0] != 2) {
            return false;
         }

         if (u[var0] != o(var1)) {
            return false;
         }
      } catch (Exception var3) {
      }

      return true;
   }

   private static void u(int var0) {
      try {
         if (s != null && w != null && t != null) {
            if (s[var0] != null) {
               s[var0].stop();
               s[var0].deallocate();
               s[var0].close();
               s[var0] = null;
               System.gc();
            }

            w[var0] = 0;
            t[var0] = 0;
         }
      } catch (Exception var2) {
      }
   }

   static void e() {
      if (t != null) {
         for(int var0 = 0; var0 < 1; ++var0) {
            d(var0);
         }

      }
   }

   static final short[] e(int var0) {
      Object var1 = null;
      h(var0);
      g();
      int var3;
      short[] var5 = new short[var3 = i()];

      for(int var4 = 0; var4 < var3; ++var4) {
         var5[var4] = (short)h();
      }

      return var5;
   }

   static final byte[] f(int var0) {
      Object var1 = null;
      h(var0);
      g();
      int var3;
      byte[] var5 = new byte[var3 = i()];

      for(int var4 = 0; var4 < var3; ++var4) {
         var5[var4] = (byte)g();
      }

      return var5;
   }

   static final byte[][] g(int var0) {
      Object var1 = null;
      h(var0);
      g();
      int var3;
      byte[][] var7 = new byte[var3 = i()][];

      for(int var4 = 0; var4 < var3; ++var4) {
         int var5;
         if ((var5 = i()) != 0) {
            var7[var4] = new byte[var5];

            for(int var6 = 0; var6 < var5; ++var6) {
               var7[var4][var6] = (byte)g();
            }
         }
      }

      return var7;
   }

   static boolean b(String var0) {
      if (var0 == A) {
         return true;
      } else {
         if (g != null) {
            f();
         }

         A = var0;

         try {
            g = A.getClass().getResourceAsStream(a + var0);
         } catch (Exception var3) {
         }

         D = 0;
         h = i();
         C = 4;
         if (B != null) {
            B = null;
         }

         B = new int[h + 1];
         if (i != null) {
            i = null;
         }

         i = new int[h];
         B[0] = 0;

         for(int var1 = 0; var1 < h; ++var1) {
            B[var1 + 1] = i();
            C += 4;
            i[var1] = B[var1 + 1] - B[var1] - 1;
         }

         return true;
      }
   }

   static void h(int var0) {
      int var1 = B[var0] + C;
      if (D == var1) {
         j = g();
      } else {
         if (D > var1) {
            if (g != null) {
               try {
                  g.close();
               } catch (Exception var3) {
               }
            }

            g = A.getClass().getResourceAsStream(a + A);
            D = 0;
         } else {
            var1 -= D;
         }

         i(var1);
         j = g();
      }
   }

   static void i(int var0) {
      try {
         if (z == null) {
            z = new byte[256];
         }

         while(var0 > 256) {
            g.read(z, 0, 256);
            D += 256;
            var0 -= 256;
         }

         if (var0 > 0) {
            g.read(z, 0, var0);
            D += var0;
         }

      } catch (Exception var2) {
      }
   }

   static void f() {
      try {
         if (A != null) {
            A = null;
         }

         if (g != null) {
            g.close();
            g = null;
         }

         D = -1;
         System.gc();
      } catch (Exception var1) {
      }
   }

   static int g() {
      int var0 = 0;

      try {
         var0 = g.read();
         ++D;
      } catch (Exception var2) {
      }

      return var0 & 255;
   }

   static int a(byte[] var0) {
      int var1 = 0;

      try {
         var1 = g.read(var0);
         D += var1;
      } catch (Exception var3) {
      }

      return var1;
   }

   static int a(byte[] var0, int var1, int var2) {
      int var3 = 0;

      try {
         var3 = g.read(var0, var1, var2);
         D += var3;
      } catch (Exception var5) {
      }

      return var3;
   }

   static byte[] j(int var0) {
      byte[] var1 = null;

      try {
         boolean var2 = false;
         if (var0 < 0 || var0 >= h) {
            return null;
         }

         h(var0);
         var1 = new byte[i[var0]];
         int var4 = g.read(var1);
         D += var4;
      } catch (Exception var3) {
      }

      return var1;
   }

   static int h() {
      return g() | g() << 8;
   }

   private static int i() {
      return g() | g() << 8 | g() << 16 | g() << 24;
   }

   static int c(int var0, int var1) {
      if (var1 != var0) {
         int var2;
         if ((var2 = l.nextInt()) < 0) {
            var2 *= -1;
         }

         return var0 + var2 % (var1 - var0);
      } else {
         return var1;
      }
   }

   static int a(int var0, int var1, int var2) {
      int var3;
      while((var3 = c(var0, var1)) == var2) {
      }

      return var3;
   }

   static int a(long var0) {
      long var4 = 0L;
      long var6 = 32768L;
      long var8 = 15L;

      do {
         long var2;
         if (var0 >= (var2 = (var4 << 1) + var6 << (int)(var8--))) {
            var4 += var6;
            var0 -= var2;
         }
      } while((var6 >>= 1) > 0L);

      return (int)var4;
   }

   private static void v(int var0) {
      try {
         g.skip((long)var0);
         D += var0;
      } catch (Exception var2) {
      }
   }

   static void a(String var0, int var1) {
      b(var0);
      h(var1);
      m = 0;
      o = null;
      n = null;
      boolean var2 = false;

      int var3;
      for(var3 = 0; var3 < h.ay; ++var3) {
         int var4 = i();
         v(4 * (var4 - 1));
         v(i());
      }

      m = i();
      if (o != null) {
         o = null;
      }

      o = new int[m + 1];

      for(var3 = 1; var3 < m + 1; ++var3) {
         o[var3] = i();
      }

      if (n != null) {
         n = null;
      }

      n = new byte[o[m]];
      a(n);
      f();
   }

   static String k(int var0) {
      if (h.h == 20 && var0 < h.az.length) {
         return h.az[var0];
      } else {
         try {
            int var1;
            if ((var1 = o[var0 + 1] - o[var0]) == 0) {
               return null;
            } else {
               int var2 = o[var0];
               if ((new String(n, var2, 1, "ISO-8859-1")).compareTo("@") == 0) {
                  if (h.s[0].a[0] == 1) {
                     return k(var0 + 1);
                  }

                  --var1;
                  ++var2;
               }

               if (h.h == 21) {
                  byte[] var4 = new byte[var1];

                  for(int var5 = 0; var5 < var1; ++var5) {
                     if (n[var2 + var5] == 124) {
                        var4[var5] = 10;
                     } else {
                        var4[var5] = n[var2 + var5];
                     }
                  }

                  return new String(var4, 0, var1, "ISO-8859-1");
               } else {
                  return new String(n, var2, var1, "ISO-8859-1");
               }
            }
         } catch (Exception var6) {
            return null;
         }
      }
   }
}
