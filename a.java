import javax.microedition.lcdui.Graphics;
import javax.microedition.lcdui.Image;

final class a {
   int a;
   byte[] b;
   byte[] c;
   byte[] d;
   short[] e;
   byte[] f;
   byte[] g;
   byte[] h;
   short[] i;
   byte[] j;
   int[][] k;
   int l;
   public int m;
   short n;
   int o;
   int p;
   byte[] q;
   short[] r;
   byte[] s;
   boolean t;
   int u;
   static int[] v = new int[4096];
   Image[][] w;
   short[] x = null;
   static int y;
   static int z;
   byte[] A;
   private int B = 0;

   final void a(byte[] var1, int var2, int var3, boolean var4) {
      try {
         ++var2;
         ++var2;
         if (((var1[var2++] & 255) + ((var1[var2++] & 255) << 8) + ((var1[var2++] & 255) << 16) + ((var1[var2++] & 255) << 24) & 16384) != 0) {
            this.t = true;
         } else {
            this.t = false;
         }

         this.a = (var1[var2++] & 255) + ((var1[var2++] & 255) << 8);
         int var7;
         if (this.a > 0) {
            this.b = new byte[this.a];
            this.c = new byte[this.a];
            if (var4) {
               this.s = new byte[this.a * 6];
            }

            for(var7 = 0; var7 < this.a; ++var7) {
               this.b[var7] = var1[var2++];
               if (this.b[var7] < 0) {
                  if (var4) {
                     this.s[var7 * 6 + 0] = var1[var2++];
                     this.s[var7 * 6 + 1] = var1[var2++];
                     this.s[var7 * 6 + 2] = var1[var2++];
                     this.s[var7 * 6 + 3] = var1[var2++];
                     this.s[var7 * 6 + 4] = var1[var2++];
                     this.s[var7 * 6 + 5] = var1[var2++];
                  } else {
                     var2 += 6;
                  }

                  this.b[var7] = 0;
                  this.c[var7] = 0;
               } else {
                  this.c[var7] = var1[var2++];
               }
            }
         }

         if ((var7 = (var1[var2++] & 255) + ((var1[var2++] & 255) << 8)) > 0) {
            if (this.t) {
               this.g = new byte[var7 * 5];
            } else {
               this.g = new byte[var7 << 2];
            }

            System.arraycopy(var1, var2, this.g, 0, this.g.length);
            var2 += this.g.length;
         }

         int var8;
         int var9;
         int var10;
         if ((var8 = (var1[var2++] & 255) + ((var1[var2++] & 255) << 8)) > 0) {
            this.d = new byte[var8];
            this.e = new short[var8];

            for(var9 = 0; var9 < var8; ++var9) {
               this.d[var9] = var1[var2++];
               ++var2;
               this.e[var9] = (short)((var1[var2++] & 255) + ((var1[var2++] & 255) << 8));
            }

            var9 = var8 << 2;
            this.f = new byte[var9];

            for(var10 = 0; var10 < var9; ++var10) {
               this.f[var10] = var1[var2++];
            }
         }

         if ((var9 = (var1[var2++] & 255) + ((var1[var2++] & 255) << 8)) > 0) {
            this.j = new byte[var9 * 5];
            System.arraycopy(var1, var2, this.j, 0, this.j.length);
            var2 += this.j.length;
         }

         if ((var10 = (var1[var2++] & 255) + ((var1[var2++] & 255) << 8)) > 0) {
            this.h = new byte[var10];
            this.i = new short[var10];

            for(int var11 = 0; var11 < var10; ++var11) {
               this.h[var11] = var1[var2++];
               ++var2;
               this.i[var11] = (short)((var1[var2++] & 255) + ((var1[var2++] & 255) << 8));
            }
         }

         if (this.a > 0) {
            short var18 = (short)((var1[var2++] & 255) + ((var1[var2++] & 255) << 8));
            this.l = var1[var2++] & 255;
            int var12;
            if ((var12 = var1[var2++] & 255) == 0) {
               var12 = 256;
            }

            if (var3 == -1) {
               this.k = new int[this.l][];
            } else {
               this.k = new int[var3][];
            }

            int var13;
            int var14;
            int var15;
            for(var13 = 0; var13 < this.l; ++var13) {
               this.k[var13] = new int[var12];
               if (var18 == -30584) {
                  for(var14 = 0; var14 < var12; ++var14) {
                     var15 = (var1[var2++] & 255) + ((var1[var2++] & 255) << 8) + ((var1[var2++] & 255) << 16) + ((var1[var2++] & 255) << 24);
                     this.k[var13][var14] = var15;
                  }
               } else if (var18 == 17476) {
                  this.k[var13][0] = 0;
                  var2 += 2;

                  for(var14 = 1; var14 < var12; ++var14) {
                     this.k[var13][var14] = (short)((var1[var2++] & 255) + ((var1[var2++] & 255) << 8));
                  }
               } else if (var18 == 21781) {
                  this.k[var13][0] = 0;
                  var2 += 2;

                  for(var15 = 1; var15 < var12; ++var15) {
                     var14 = (var1[var2++] & 255) + ((var1[var2++] & 255) << 8);
                     this.k[var13][var15] = -16777216 | (var14 & 31744) << 9 | (var14 & 992) << 6 | (var14 & 31) << 3;
                  }
               }
            }

            this.n = (short)((var1[var2++] & 255) + ((var1[var2++] & 255) << 8));
            if (this.a > 0) {
               this.r = new short[this.a + 1];
               var13 = 0;
               var14 = var2;

               int var16;
               for(var15 = 0; var15 < this.a; ++var15) {
                  var16 = (var1[var14++] & 255) + ((var1[var14++] & 255) << 8);
                  this.r[var15] = (short)var13;
                  var14 += var16;
                  var13 += var16;
               }

               this.r[this.a] = (short)var13;
               this.q = new byte[var13];

               for(var15 = 0; var15 < this.a; ++var15) {
                  var16 = (var1[var2++] & 255) + ((var1[var2++] & 255) << 8);
                  System.arraycopy(var1, var2, this.q, this.r[var15] & '\uffff', var16);
                  var2 += var16;
               }
            }

         }
      } catch (Exception var17) {
      }
   }

   final void a(int var1, int var2, int var3, int var4, int var5) {
      if (this.a != 0) {
         if (var3 == -1) {
            var3 = this.a - 1;
         }

         if (v == null) {
            v = new int[4096];
         }

         if (this.w == null) {
            this.w = new Image[this.l][];
         }

         int var6;
         int var7;
         if (this.t && this.g != null) {
            var6 = this.g.length;
            if (this.x == null) {
               this.x = new short[this.a];

               for(int var9 = 0; var9 < var6; var9 += 5) {
                  var7 = this.g[var9] & 255;
                  byte var8 = this.g[var9 + 3];
                  short[] var10000 = this.x;
                  var10000[var7] = (short)(var10000[var7] | 1 << var8);
               }
            }
         }

         if (!this.t && this.w[var5] == null) {
            this.w[var5] = new Image[this.a];
         }

         if (var4 >= 0) {
            for(var6 = var2; var6 <= var3; ++var6) {
               this.w[var5][var6] = this.w[var4][var6];
            }
         } else {
            var6 = var1 + 1;
            if (this.t) {
               var1 = 0;
               var6 = this.l;
            }

            var7 = this.m;

            for(int var16 = var1; var16 < var6; ++var16) {
               if (this.t && this.w[var5] == null) {
                  this.w[var5] = new Image[this.a];
               }

               if (this.t) {
                  this.m = var16;
               } else {
                  this.m = var1;
               }

               for(int var10 = var2; var10 <= var3; ++var10) {
                  if (this.x == null || (this.x[var10] >> var16 & 1) != 0) {
                     int var11 = this.b[var10] & 255;
                     int var12 = this.c[var10] & 255;
                     int[] var13;
                     if (var11 > 0 && var12 > 0 && (var13 = this.d(var10)) != null) {
                        if (this.t) {
                           this.w[var5][var10] = Image.createRGBImage(var13, var11, var12, true);
                        } else {
                           this.w[var5][var10] = Image.createRGBImage(var13, var11, var12, true);
                        }
                     }
                  }
               }
            }

            this.m = var7;
         }

      }
   }

   final void a(Graphics var1, int var2, int var3, int var4, int var5) {
      int var6 = this.b[var2] & 255;
      boolean var7 = false;
      int var10;
      if (var6 <= 0) {
         if (this.s != null) {
            var5 = var2 * 6;
            var6 = this.s[var5 + 4] & 255;
            var10 = this.s[var5 + 5] & 255;
            if (var6 == 1 && var10 == 1) {
               try {
                  h.a(var1, this.u, this.s[var5 + 2] & 255, this.s[var5 + 0] & 255, var3, var4);
                  return;
               } catch (Exception var9) {
                  return;
               }
            }

            var1.setColor(this.s[var5 + 2] & 255, this.s[var5 + 1] & 255, this.s[var5 + 0] & 255);
            var1.fillRect(var3, var4, var6, var10);
         }

      } else {
         Image var8 = null;
         if (this.w != null && this.w[this.m] != null) {
            var8 = this.w[this.m][var2];
         }

         var6 = var8.getWidth();
         var10 = var8.getHeight();
         if ((var5 & 1) != 0) {
            if ((var5 & 2) != 0) {
               var1.drawRegion(var8, 0, 0, var6, var10, 3, var3, var4, 0);
            } else {
               var1.drawRegion(var8, 0, 0, var6, var10, 2, var3, var4, 0);
            }
         } else if ((var5 & 2) != 0) {
            var1.drawRegion(var8, 0, 0, var6, var10, 1, var3, var4, 0);
         } else {
            var1.drawRegion(var8, 0, 0, var6, var10, 0, var3, var4, 0);
         }
      }
   }

   private int[] d(int var1) {
      int var2 = this.b[var1] & 255;
      int var3 = this.c[var1] & 255;
      int[] var4 = v;
      int[] var5;
      if ((var5 = this.k[this.m]) == null) {
         return null;
      } else {
         byte[] var6 = this.q;
         int var7 = this.r[var1] & '\uffff';
         int var8 = 0;
         int var9 = var2 * var3;
         int var10;
         int var11;
         if (this.n != 25840) {
            if (this.n == 10225) {
               while(true) {
                  while(var8 < var9) {
                     if ((var10 = var6[var7++] & 255) > 127) {
                        var11 = var6[var7++] & 255;
                        int var12 = var5[var11];

                        for(var10 -= 128; var10-- > 0; var4[var8++] = var12) {
                        }
                     } else {
                        var4[var8++] = var5[var10];
                     }
                  }

                  return var4;
               }
            } else if (this.n == 22258) {
               while(true) {
                  while(var8 < var9) {
                     if ((var10 = var6[var7++] & 255) > 127) {
                        for(var10 -= 128; var10-- > 0; var4[var8++] = var5[var6[var7++] & 255]) {
                        }
                     } else {
                        for(var11 = var5[var6[var7++] & 255]; var10-- > 0; var4[var8++] = var11) {
                        }
                     }
                  }

                  return var4;
               }
            } else if (this.n == 5632) {
               while(var8 < var9) {
                  var4[var8++] = var5[var6[var7] >> 4 & 15];
                  var4[var8++] = var5[var6[var7] & 15];
                  ++var7;
               }
            } else if (this.n == 1024) {
               while(var8 < var9) {
                  var4[var8++] = var5[var6[var7] >> 6 & 3];
                  var4[var8++] = var5[var6[var7] >> 4 & 3];
                  var4[var8++] = var5[var6[var7] >> 2 & 3];
                  var4[var8++] = var5[var6[var7] & 3];
                  ++var7;
               }
            } else if (this.n == 512) {
               while(var8 < var9) {
                  var4[var8++] = var5[var6[var7] >> 7 & 1];
                  var4[var8++] = var5[var6[var7] >> 6 & 1];
                  var4[var8++] = var5[var6[var7] >> 5 & 1];
                  var4[var8++] = var5[var6[var7] >> 4 & 1];
                  var4[var8++] = var5[var6[var7] >> 3 & 1];
                  var4[var8++] = var5[var6[var7] >> 2 & 1];
                  var4[var8++] = var5[var6[var7] >> 1 & 1];
                  var4[var8++] = var5[var6[var7] & 1];
                  ++var7;
               }
            } else if (this.n == 22018) {
               while(var8 < var9) {
                  var4[var8++] = var5[var6[var7++] & 255];
               }
            }
         } else {
            while(var8 < var9) {
               var10 = var6[var7++] & 255;
               var11 = var5[var10 & this.o];

               for(var10 >>= this.p; var10-- >= 0; var4[var8++] = var11) {
               }
            }
         }

         return var4;
      }
   }

   final void a() {
      this.w = (Image[][])null;
   }

   final int a(int var1, int var2) {
      return this.j[(this.i[var1] + var2) * 5 + 1] & 255;
   }

   final int a(int var1) {
      return this.f[var1 * 4 + 2] & 255;
   }

   final int b(int var1) {
      return this.f[var1 * 4 + 3] & 255;
   }

   final int b(int var1, int var2) {
      int var3 = this.e[var1] + var2 << 2;
      return this.g[var3 + 1];
   }

   final int c(int var1, int var2) {
      int var3 = this.e[var1] + var2 << 2;
      return this.g[var3 + 2];
   }

   final int d(int var1, int var2) {
      int var3 = (this.i[var1] + var2) * 5;
      return this.j[var3] & 255;
   }

   final void a(int[] var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8) {
      int var9 = (this.i[var2] + var3) * 5;
      int var10 = this.j[var9] & 255;
      if ((var6 & 1) != 0) {
         var7 += this.j[var9 + 2];
      } else {
         var7 -= this.j[var9 + 2];
      }

      if ((var6 & 2) != 0) {
         var8 += this.j[var9 + 3];
      } else {
         var8 -= this.j[var9 + 3];
      }

      this.a(var1, var10, var4, var5, var6 ^ this.j[var9 + 4] & 15, var7, var8);
   }

   final void a(int[] var1, int var2, int var3, int var4, int var5, int var6, int var7) {
      int var8 = var2 << 2;
      byte var9 = this.f[var8++];
      byte var10 = this.f[var8++];
      int var11 = this.f[var8++] & 255;
      int var12 = this.f[var8] & 255;
      if ((var5 & 1) != 0) {
         var6 += var9 + var11;
      } else {
         var6 -= var9;
      }

      if ((var5 & 2) != 0) {
         var7 += var10 + var12;
      } else {
         var7 -= var10;
      }

      var1[0] = var3 - (var6 << 8);
      var1[1] = var4 - (var7 << 8);
      var1[2] = var1[0] + (var11 << 8);
      var1[3] = var1[1] + (var12 << 8);
   }

   final void a(int[] var1, int var2, int var3, int var4) {
      var1[0] = var3;
      var1[1] = var4;
      var1[2] = var3 + ((this.b[var2] & 255) << 8);
      var1[3] = var4 + ((this.c[var2] & 255) << 8);
   }

   final int[] a(int var1, int var2, int[] var3, int var4, int var5) {
      int var6 = (this.i[var1] + var2) * 5;
      int var7 = this.j[var6] & 255;
      if ((var4 & 1) != 0) {
         var3[0] += -this.j[var6 + 2];
      } else {
         var3[0] -= -this.j[var6 + 2];
      }

      if ((var4 & 2) != 0) {
         var3[1] += -this.j[var6 + 3];
      } else {
         var3[1] -= -this.j[var6 + 3];
      }

      int var8 = this.d[var7] & 255;

      for(int var9 = 0; var9 < var8; ++var9) {
         var6 = this.e[var7] + var9 << 2;
         int var10 = this.g[var6 + 3] & 255;
         int var11 = this.g[var6] & 255 | (var10 & 192) << 2;
         int var12 = this.b[var11] & 255;
         int var13 = this.c[var11] & 255;
         if (var12 <= 0 || var13 <= 0) {
            if (var5 == 0) {
               if ((var4 & 1) != 0) {
                  var3[0] -= this.g[var6 + 1];
               } else {
                  var3[0] += this.g[var6 + 1];
               }

               if ((var4 & 2) != 0) {
                  var3[1] -= this.g[var6 + 2];
               } else {
                  var3[1] += this.g[var6 + 2];
               }

               return var3;
            }

            --var5;
         }
      }

      return null;
   }

   final void a(Graphics var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8) {
      int var9 = (this.i[var2] + var3) * 5;
      int var10 = this.j[var9] & 255;
      if ((var6 & 1) != 0) {
         var7 += this.j[var9 + 2];
      } else {
         var7 -= this.j[var9 + 2];
      }

      if ((var6 & 2) != 0) {
         var8 += this.j[var9 + 3];
      } else {
         var8 -= this.j[var9 + 3];
      }

      this.a(var1, var10, var4 - var7, var5 - var8, var6 ^ this.j[var9 + 4] & 15, var7, var8);
   }

   final void a(Graphics var1, int var2, int var3, int var4, int var5, int var6, int var7) {
      int var8 = this.d[var2] & 255;

      for(int var9 = 0; var9 < var8; ++var9) {
         this.u = var9;
         this.a(var1, var2, var9, var3, var4, var5);
      }

   }

   private void a(Graphics var1, int var2, int var3, int var4, int var5, int var6) {
      int var7;
      if (this.t) {
         var7 = (this.e[var2] + var3) * 5;
      } else {
         var7 = this.e[var2] + var3 << 2;
      }

      int var8 = this.g[var7 + (this.t ? 4 : 3)] & 255;
      int var9 = this.g[var7] & 255 | (var8 & 192) << 2;
      if ((var6 & 1) != 0) {
         var4 -= this.g[var7 + 1];
      } else {
         var4 += this.g[var7 + 1];
      }

      if ((var6 & 2) != 0) {
         var5 -= this.g[var7 + 2];
      } else {
         var5 += this.g[var7 + 2];
      }

      if ((var6 & 1) != 0) {
         var4 -= this.b[var9] & 255;
      }

      if ((var6 & 2) != 0) {
         var5 -= this.c[var9] & 255;
      }

      this.a(var1, var9, var4, var5, var6 ^ var8 & 15);
   }

   final void b() {
      this.q = null;
      this.r = null;
      System.gc();
   }

   final void c() {
      if (this.k != null) {
         for(int var1 = 0; var1 < this.k.length; ++var1) {
            if (this.k[var1] != null) {
               this.k[var1] = null;
            }
         }

         this.k = (int[][])null;
      }

      System.gc();
   }

   final void c(int var1) {
      this.B = var1;
   }

   final void a(String var1) {
      y = 0;
      z = this.c[0] & 255;
      int var3 = 0;
      boolean var4 = false;

      for(int var2 = 0; var2 < var1.length(); ++var2) {
         int var5;
         if ((var5 = var1.charAt(var2)) == 123) {
            var4 = true;
            var5 = -1;
         } else if (var5 == 125) {
            var4 = false;
            var5 = -1;
         } else if (var5 > 32) {
            var5 = this.A[var5] & 255;
         } else {
            if (var5 == 32) {
               var3 += (this.b[0] & 255) + this.g[1];
               continue;
            }

            if (var5 == 10) {
               if (var3 > y) {
                  y = var3;
               }

               var3 = 0;
               z += this.B + (this.c[0] & 255);
               continue;
            }
         }

         if (var5 >= 0) {
            int var6 = this.g[var5 << 2] & 255;
            if (var2 == var1.length() - 1) {
               var3 += (this.b[var6] & 255) - this.g[(var5 << 2) + 1];
            } else {
               var3 += (this.b[var6] & 255) - this.g[(var5 << 2) + 1] + this.g[1];
            }
         }

         if (var4) {
            var3 += this.g[1];
         }
      }

      if (var3 > y) {
         y = var3;
      }

      if (y > 0) {
         y -= this.g[1];
      }

   }

   final void a(Graphics var1, String var2, int var3, int var4, int var5, int var6) {
      this.m = var6;
      var4 -= this.g[2];
      int var7;
      if (h.h == 21 && (var7 = var2.indexOf(37)) != -1) {
         String var8 = GloftMMN.b.getAppProperty("MIDlet-Version");
         var2 = var2.substring(0, var7) + var8 + var2.substring(var7 + 1, var2.length());
      }

      boolean var16 = false;
      if ((var5 & 42) != 0) {
         this.a(var2);
         if ((var5 & 8) != 0) {
            var3 -= y;
         }

         if ((var5 & 32) != 0) {
            var4 -= z + 1;
         } else if ((var5 & 2) != 0) {
            var4 -= z >> 1;
         }
      }

      int var9 = var3;
      int var10 = var4;
      boolean var11 = false;

      for(int var12 = 0; var12 < var2.length(); ++var12) {
         if (!var16 && (var5 & 1) != 0) {
            var16 = true;
            if ((var7 = var2.indexOf(10, var12)) != -1) {
               this.a(var2.substring(var12, var7));
            } else {
               this.a(var2.substring(var12, var2.length()));
            }

            var9 -= y >> 1;
         }

         int var13;
         if ((var13 = var2.charAt(var12) & 255) == 32) {
            var9 += (this.b[0] & 255) + this.g[1];
         } else if (var13 == 10) {
            var16 = false;
            var9 = var3;
            var10 += this.B + (this.c[0] & 255);
         } else {
            if (var13 == 123) {
               var13 = -1;
               var11 = true;
            } else if (var13 == 125) {
               var13 = -1;
               var11 = false;
            } else {
               try {
                  var13 = this.A[var13] & 255;
               } catch (Exception var15) {
                  var13 = 0;
               }
            }

            if (var13 > 0) {
               int var14 = this.g[var13 << 2] & 255;
               this.a(var1, 0, var13, var9, var10, 0);
               if (var11) {
                  this.a(var1, 0, var13, var9 + 1, var10, 0);
                  var9 += this.g[1];
               }

               var9 += (this.b[var14] & 255) - this.g[(var13 << 2) + 1] + this.g[1];
            }
         }
      }

   }

   final void b(Graphics var1, String var2, int var3, int var4, int var5, int var6) {
      int var7 = 0;
      int var8 = var2.length();
      int[] var9 = new int[100];

      int var10;
      for(var10 = 0; var10 < var8; ++var10) {
         if (var2.charAt(var10) == '\n' || var2.charAt(var10) == '|') {
            var9[var7++] = var10;
         }
      }

      var9[var7++] = var8;
      boolean var11 = false;
      int var12 = this.B + (this.c[0] & 255);
      if ((var5 & 32) != 0) {
         var4 -= var12 * (var7 - 1);
      } else if ((var5 & 2) != 0) {
         var4 -= var12 * (var7 - 1) >> 1;
      }

      for(int var13 = 0; var13 < var7; ++var13) {
         var10 = var13 > 0 ? var9[var13 - 1] + 1 : 0;
         int var14 = var9[var13];
         this.a(var1, var2.substring(var10, var14), var3, var4 + var13 * var12, var5, var6);
      }

   }
}
