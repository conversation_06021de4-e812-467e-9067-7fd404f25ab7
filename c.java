import java.io.InputStream;
import java.util.Hashtable;
import javax.microedition.io.ConnectionNotFoundException;
import javax.microedition.lcdui.Canvas;
import javax.microedition.lcdui.Command;
import javax.microedition.lcdui.CommandListener;
import javax.microedition.lcdui.Displayable;
import javax.microedition.lcdui.Font;
import javax.microedition.lcdui.Graphics;
import javax.microedition.lcdui.Image;
import javax.microedition.midlet.MIDlet;
import javax.microedition.rms.RecordStore;

public final class c implements Runnable, CommandListener {
   public static String a = "2.0";
   public static String b;
   private static String g;
   private static String h;
   private static Font i;
   private static int j;
   private static int k;
   private static int l;
   private static int m;
   private static int n;
   private static int o;
   private static boolean[] p;
   private static int[] q;
   private static int r;
   public static int c;
   public static int d;
   private static int s;
   private static int t;
   private static int u;
   private static int v;
   private static int w;
   private static int x;
   private static int y;
   private static int z;
   private static int A;
   private static int B;
   private static int C;
   private static int D;
   private static int E;
   private static int F;
   private static int G;
   private static int H;
   private static int I;
   private static int[] J;
   private static byte[][] K;
   private static int L;
   private static byte[] M;
   private static int N;
   private static int O;
   private static int P;
   private static int Q;
   private static int R;
   private static Image[] S;
   private static int T;
   private static int[] U;
   private static int V;
   private static int W;
   private static boolean X;
   private static final String[] Y;
   private static String Z;
   private static boolean aa;
   public static String[] e;
   private static String[] ab;
   private static String[] ac;
   private static String[] ad;
   private static int ae;
   private static int af;
   private static String[] ag;
   private static short[] ah;
   private static int ai;
   private static int aj;
   private static int ak;
   private static boolean al;
   private static boolean am;
   private static MIDlet an;
   private static Canvas ao;
   private static String ap;
   private static boolean aq;
   private static CommandListener ar;
   public static c f;
   private static boolean as;
   private static String at;
   private static boolean au;
   private static int av;
   private static int aw;
   private static int ax;
   private static int ay;
   private static int az;
   private static Image[] aA;
   private static Image[] aB;
   private static Image aC;
   private static Image aD;
   private static Image aE;
   private static Image aF;
   private static Image aG;
   private static Image aH;
   private static Image aI;
   private static Image aJ;
   private static Image aK;
   private static Image[] aL;
   private static Image aM;
   private static Image[][] aN;
   private static String[][] aO;
   private static int[][] aP;
   private static int[][] aQ;
   private static int[] aR;
   private static int aS;
   private static int aT;
   private static int aU;
   private static int aV;
   private static int aW;
   private static int aX;
   private static int aY;
   private static int aZ;
   private static int ba;
   private static int bb;
   private static int bc;
   private static String[] bd;
   private static boolean[] be;
   private static int bf;
   private static int bg;
   private static boolean bh;
   private static byte bi;
   private static byte bj;
   private static boolean bk;
   private static int bl;
   private static int bm;
   private static int bn;
   private static int bo;
   private static int bp;
   private static int bq;
   private static String br;
   private static String bs;
   private static String bt;
   private static int bu;
   private static int bv;
   private static int bw;
   private static int bx;
   private static Command by;
   private static Command bz;
   private static int bA;
   private static boolean bB;
   private static boolean bC;
   private static int bD;
   private static int bE;
   private static int bF;
   private static int bG;
   private static int bH;
   private static int bI;
   private static int bJ;
   private static Hashtable bK;
   private static int bL;

   private static boolean b() {
      c();

      try {
         InputStream var0 = null;
         I = (var0 = "a".getClass().getResourceAsStream("/dataIGP")).read() & 255;
         I += (var0.read() & 255) << 8;
         J = new int[I];

         for(int var1 = 0; var1 < I; ++var1) {
            J[var1] = var0.read() & 255;
            int[] var10000 = J;
            var10000[var1] += (var0.read() & 255) << 8;
            var10000 = J;
            var10000[var1] += (var0.read() & 255) << 16;
            var10000 = J;
            var10000[var1] += (var0.read() & 255) << 24;
         }

         var0.close();
         return true;
      } catch (Exception var2) {
         return false;
      }
   }

   private static void c() {
      J = null;
      K = (byte[][])null;
      I = 0;
      System.gc();
   }

   private static byte[] b(int var0) {
      if (var0 >= 0 && var0 < I - 1) {
         int var1;
         if ((var1 = J[var0 + 1] - J[var0]) == 0) {
            return null;
         } else if (K != null) {
            return K[var0];
         } else {
            byte[] var2 = null;

            try {
               InputStream var3 = null;
               (var3 = "a".getClass().getResourceAsStream("/dataIGP")).skip((long)(2 + 4 * I + J[var0]));

               for(int var4 = (var2 = new byte[var1]).length; var4 > 0; var4 -= var3.read(var2)) {
               }

               var3.close();
            } catch (Exception var5) {
            }

            return var2;
         }
      } else {
         return null;
      }
   }

   private static int a(byte[] var0) {
      return (var0[L++] & 255) + ((var0[L++] & 255) << 8);
   }

   private static byte[] b(byte[] var0) {
      int var1;
      byte[] var2 = new byte[var1 = a(var0)];
      System.arraycopy(var0, L, var2, 0, var1);
      L += var1;
      return var2;
   }

   private static Image c(byte[] var0) {
      byte[] var1;
      return Image.createImage(var1 = b(var0), 0, var1.length);
   }

   private static Image a(byte[] var0, int var1, int var2) {
      if (e.h) {
         byte[] var3 = new byte[var2];
         System.arraycopy(var0, var1, var3, 0, var2);
         var0 = var3;
         var1 = 0;
      }

      return Image.createImage(var0, var1, var2);
   }

   private static String c(int var0) {
      return "" + ag[var0];
   }

   private static int d(int var0, int var1) {
      if (bL != 0) {
         if (var1 != O && var1 != P) {
            return M[var0 * 6 + var1] & 255;
         } else {
            int var2 = M[var0 * 6 + var1] & 255;
            int var3 = M[var0 * 6 + var1 + 1] & 255;
            boolean var4 = false;
            int var5;
            return (var5 = 0 | (var3 & 255) << 8) | var2 & 255;
         }
      } else {
         return M[(var0 << 2) + var1] & 255;
      }
   }

   private static void a(int var0, Graphics var1, int var2, int var3, int var4) {
      a(c(var0), var1, bu, var2, var3, var4);
   }

   private static void a(String var0, Graphics var1, int var2, int var3, int var4, int var5) {
      a(var0, var1, var3, var4, var5);
   }

   private static void a(String var0, Graphics var1, int var2, int var3, int var4) {
      String var5 = var0;
      U[0] = 0;
      W = 0;
      int var6 = 0;
      boolean var9 = false;
      boolean var10 = false;
      int var11 = var0.length();
      int var12 = 0;
      if (bu <= 176 && (ax == 0 || ax == 1 || ax == 2)) {
         var12 += 2;
      }

      char var7;
      int var8;
      int[] var10000;
      for(var8 = 0; var8 < var11; ++var8) {
         label121: {
            if ((var7 = var5.charAt(var8)) != '\n' || var6 >= 10) {
               if (var7 != '\\') {
                  break label121;
               }

               ++var8;
               if (var5.charAt(var8) != 'N') {
                  break label121;
               }
            }

            var10000 = U;
            var10000[var6] -= o;
            if (U[var6] > W) {
               W = U[var6];
            }

            ++var6;
            U[var6] = 0;
            continue;
         }

         if (var7 != 0 && var7 != 1) {
            var10000 = U;
            var10000[var6] += d(var7, Q) + o;
         }
      }

      var10000 = U;
      var10000[var6] -= o;
      if (U[var6] > W) {
         W = U[var6];
      }

      V = (var6 + 1) * N + var6 * (0 + var12);
      if (X) {
         X = false;
      } else {
         var3 += (0 + var12) * var6 / 2;
         var6 = 0;
         if ((var4 & 32) != 0) {
            var3 -= V;
         } else if ((var4 & 2) != 0) {
            var3 -= V >> 1;
         }

         int var13 = var2;
         var9 = true;
         var10 = false;
         b(var1, 0, 0, bu, bv);

         for(var8 = 0; var8 < var11; ++var8) {
            var7 = var5.charAt(var8);
            if (var9) {
               var13 = var2;
               if ((var4 & 8) != 0) {
                  var13 = var2 - U[var6];
               } else if ((var4 & 1) != 0) {
                  var13 = var2 - (U[var6] >> 1);
               }

               var9 = false;
            }

            if (var7 != '\n' || var6 >= 10) {
               label118: {
                  if (var7 == '\\') {
                     ++var8;
                     if (var5.charAt(var8) == 'N') {
                        break label118;
                     }
                  }

                  if (var10) {
                     var10 = false;
                     char var14;
                     if ((var14 = var5.charAt(var8 - 2)) == ' ') {
                        var13 -= d(var14, Q) + o >> 1;
                     }

                     if (var7 == ' ') {
                        var13 += d(var7, Q) + o >> 1;
                        continue;
                     }
                  }

                  b(var1, var13, var3, d(var7, Q), d(var7, R));
                  a(var1, S[T], d(var7, O), d(var7, P), d(var7, Q), d(var7, R), var13, var3);
                  var13 += d(var7, Q) + o;
                  continue;
               }
            }

            var3 += N + 0 + var12 - 2;
            ++var6;
            var9 = true;
            var10 = true;
         }

         b(var1, 0, 0, bu, bv);
         T = 1;
      }
   }

   public static void a(MIDlet var0, Canvas var1, int var2, int var3) {
      a(var0, var1, var2, var3, (CommandListener)null);
   }

   private static void a(MIDlet var0, Canvas var1, int var2, int var3, CommandListener var4) {
      (new StringBuffer()).append("initialize(midlet = ").append(var0).append(", game = ").append(var1).append(", screenWidth = ").append(var2).append(", screenHeight = ").append(var3).append(", cmdListener = ").append(var4).append(")").toString();
      bu = var2;
      bv = var3;
      bw = bu >> 1;
      bx = bv >> 1;
      l = bv * 5 / 100;
      m = bv / 2;
      n = bv * 93 / 100;
      if (e.d < 0 || e.d > bu / 2 - bu * 15 / 100) {
         k = 2;
      }

      if (an == null) {
         if (var1 != null) {
            if (var4 != null) {
               aq = true;
               if (f == null) {
                  f = new c();
               }

               ar = var4;
            }

            an = var0;
            ao = var1;
            if (e.s) {
               n();
            }

            d();
            String var5 = b;
            (new StringBuffer()).append(var5).append("").toString();
         }
      }
   }

   private static boolean b(String var0, int var1) {
      if (var0 == null) {
         return (var1 & 1) == 0;
      } else {
         var0 = var0.trim();
         return ((var1 & 1) == 0 || var0.length() != 0) && ((var1 & 2) == 0 || var0.toUpperCase().compareTo("DEL") != 0) && ((var1 & 4) == 0 || var0.toUpperCase().compareTo("NO") != 0 && var0.toUpperCase().compareTo("0") != 0);
      }
   }

   private static String a(String var0, String var1, String var2) {
      String var3 = "";

      try {
         if (var2 != null && var0 != null && var1 != null) {
            int var4 = var0.indexOf(var1 + "=");
            var2 = var2.trim();
            if (var4 >= 0 && var2.length() > 0) {
               var4 += var1.length() + 1;
               int var5;
               if ((var5 = var0.indexOf(";", var4)) < 0) {
                  var5 = var0.length();
               }

               if ((var3 = var0.substring(var4, var5).trim()).length() != 0 && var3.compareTo("0") != 0 && var3.toUpperCase().compareTo("NO") != 0) {
                  if (var3.toUpperCase().compareTo("DEL") != 0 && var1.compareTo("OP") != 0) {
                     int var6 = var2.indexOf("XXXX");
                     var3 = var2.substring(0, var6) + var3 + var2.substring(var6 + "XXXX".length());
                  }
               } else {
                  var3 = "";
               }
            }
         }
      } catch (Exception var7) {
         var3 = "";
      }

      return var3;
   }

   private static void a(int var0, String var1, int var2, String var3, String var4) {
      try {
         String var5 = null;
         if (aa) {
            var5 = a(a(var3), var1, var4);
         } else {
            var5 = a("URL-" + var1);
         }

         if (b(var5, var2) && (var5.toUpperCase().compareTo("NO") != 0 || var5.toUpperCase().compareTo("0") != 0)) {
            be[var0] = true;
            bd[var0] = var5;
            if (be[var0] && var0 != 3) {
               ++bf;
               StringBuffer var10000 = new StringBuffer();
               String[] var10002 = bd;
               var10002[var0] = var10000.append(var10002[var0]).append("&ctg=SC").append(bf < 10 ? "0" : "").append(bf).toString();
            }
         }

      } catch (Exception var7) {
      }
   }

   private static void a(int var0, String[] var1, int var2, String var3) {
      int var4 = var1.length;
      aO[var0] = new String[var4];
      aP[var0] = new int[var4];
      aQ[var0] = new int[var4];
      int var5 = 0;
      if (!p[0]) {
         String var6 = "";
         if (aa) {
            try {
               var3 = a(var3);
               if (var0 != 2) {
                  var6 = Z;
               } else if (bt.length() > 0) {
                  var6 = bt + "&ctg=XXXX";
               }
            } catch (Exception var9) {
            }
         }

         for(int var7 = 0; var7 < var1.length; ++var7) {
            try {
               String var8 = "";
               if (var0 != 2 && var7 == var4 - 1) {
                  if (!aa) {
                     var8 = a(Y[var0]);
                  } else if (bt.length() > 0) {
                     var8 = a(a("IGP-CATEGORIES"), var1[var7], bt + "&ctg=XXXX");
                  }
               } else if (aa) {
                  if (var1[var7].compareTo("GLDT") == 0) {
                     var8 = a(var3, var1[var7], Z);
                  } else if (var1[var7].compareTo("CATALOG") == 0) {
                     var8 = bt;
                  } else {
                     var8 = a(var3, var1[var7], var6);
                  }
               } else {
                  var8 = a(Y[var0] + "-" + var1[var7]);
               }

               if (b(var8, 7)) {
                  aO[var0][var5] = var8;
                  aP[var0][var5++] = var7;
                  aQ[var0][var7] = var2 + var7;
               }
            } catch (Exception var10) {
            }
         }

         if (var5 > 0) {
            be[4 + var0] = true;
            aR[var0] = var5;
         }

      }
   }

   private static String[] d(byte[] var0) {
      String[] var1 = new String[a(var0)];

      for(int var2 = 0; var2 < var1.length; ++var2) {
         int var3 = a(var0);
         var1[var2] = new String(var0, L, var3);
         L += var3;
      }

      return var1;
   }

   private static void a(String[] var0) {
      int var1 = (var0.length - 1 > 0 ? var0.length - 1 : 0) + 4;
      ++var1;
      s = var1;
      t = s + 1;
      u = t + 1;
      v = u + 1;
      w = v + 1;
      x = w + 1;
      y = x + 1;
      z = y + 1;
      A = z + 1;
      B = A + 1;
      C = B + 1;
      D = C + 1;
      E = D + 1;
      F = E + 1;
      G = F + ab.length;
   }

   private static void d() {
      String[] var0;
      try {
         if (!b()) {
            au = false;
            return;
         }

         byte[] var2;
         a(var2 = b(0));
         int var3 = a(var2);
         new String(var2, L, var3);
         L += var3;
         e = d(var2);
         var0 = d(var2);
         ab = d(var2);
         ac = d(var2);
         ad = d(var2);

         int var4;
         for(var4 = 0; var4 < p.length; ++var4) {
            p[var4] = a(var2) == 1;
         }

         try {
            var4 = a(var2);
            g = new String(var2, L, var4);
            if (g.equals("2.0z")) {
               bL = 2;
               ai = 12;
               aj = 6;
               P = 2;
               Q = 4;
               R = 5;
            }

            if (!g.startsWith(a)) {
               (new StringBuffer()).append("Invalid dataIGP file, dataIGP file IGP Version : ").append(g).toString();
               (new StringBuffer()).append("IGP Class version : ").append(a).toString();
            }
         } catch (Exception var8) {
            au = false;
         }

         c();
      } catch (Exception var9) {
         au = false;
         return;
      }

      a(var0);
      ae = var0.length;
      aL = new Image[ae];
      af = ae + 1 + 1 + 1 + 1 + (p[0] ? 1 : 0);

      int var1;
      for(var1 = 0; var1 < p.length; ++var1) {
         if (p[var1]) {
            q[var1] = ++d;
         } else {
            q[var1] = --c;
         }
      }

      r = ++d;
      bd = new String[9];
      be = new boolean[9];

      for(var1 = 0; var1 < be.length; ++var1) {
         be[var1] = false;
      }

      aO = new String[3][];
      aP = new int[3][];
      aQ = new int[3][];
      aR = new int[3];

      try {
         Z = a("URL-TEMPLATE-GAME").trim();
         aa = true;
      } catch (Exception var7) {
      }

      for(var1 = 0; var1 < ae; ++var1) {
         a(var1, var0[var1], 7, "IGP-PROMOS", Z);
      }

      String var11 = null;

      try {
         if (b(var11 = a("URL-OPERATOR"), 7)) {
            bt = var11;
         }

         br = a("URL-PT");
      } catch (Exception var6) {
      }

      if (!p[0]) {
         if (aa) {
            if (b(bt, 7)) {
               a(ae, "PROMO", 7, "IGP-CATEGORIES", bt + "&ctg=XXXX");
            }
         } else {
            String var10;
            if ((var10 = a("URL-PROMO")) != null) {
               var10.trim();
               if (b(a("URL-PROMO"), 7)) {
                  bd[3] = var10;
                  be[3] = true;
               }
            }
         }
      }

      a(0, ab, F, "IGP-WN");
      a(1, ac, G, "IGP-BS");
      if (!p[0]) {
         if (aa) {
            if (b(a(a("IGP-CATEGORIES"), "OP", bt), 7)) {
               if (b(bt, 7)) {
                  be[6] = true;
               }

               bd[6] = bt;
            }
         } else if (b(bt, 7)) {
            bd[6] = bt;
            be[6] = true;
         }

         if (be[6]) {
            StringBuffer var10000 = new StringBuffer();
            String[] var10002 = bd;
            var10002[6] = var10000.append(var10002[6]).append("&ctg=CCTL").toString();
         }
      }

      if (p[0]) {
         try {
            if (b(bt, 7)) {
               bd[8] = bt;
               be[8] = true;
            }
         } catch (Exception var5) {
         }
      }

      bc = f();
      if (bc > 0) {
         au = true;
      }

      (new StringBuffer()).append("isAvailable = ").append(au).toString();
   }

   private static int e() {
      if (!au) {
         return -1;
      } else {
         return f() > 0 ? 0 : -1;
      }
   }

   public static boolean a() {
      return e() != -1;
   }

   public static void a(String var0, int var1) {
      (new StringBuffer()).append("enterIGP(loadingMsg = ").append(var0).append(", appLanguage = ").append(var1).append(" (").append(e[var1]).append(")").toString();
      c(var0, var1);
      if (e.f == 0) {
         az = 4 + af;
      } else {
         az = 4;
      }

      l();
      ax = g();
      if (aq) {
         ao.setCommandListener(f);
      }

   }

   private static void c(String var0, int var1) {
      if (var1 >= 0 && var1 < e.length) {
         bq = var1 <= e.length ? var1 : 0;
         bs = var0;
         ay = -1;
         av = 0;
         ax = 0;
         aV = 0;
         aW = 0;
         U = new int[10];
         as = true;
         i = Font.getFont(0, 0, 8);
         bi = 0;
         bj = 0;
         if (e.j == 2) {
            (new Thread(new c())).start();
         }

      }
   }

   private static int f() {
      int var0 = 0;

      for(int var1 = 0; var1 < be.length; ++var1) {
         if (be[var1]) {
            ++var0;
         }
      }

      return var0;
   }

   private static int d(int var0) {
      switch(var0) {
      case 6:
         return 10;
      case 8:
         return q[0];
      default:
         return 4 + var0;
      }
   }

   private static int g() {
      for(int var0 = 0; var0 < be.length; ++var0) {
         if (be[var0]) {
            return var0;
         }
      }

      return -1;
   }

   private static void h() {
      if (br != null) {
         int var0;
         if ((var0 = br.length()) <= 0) {
            br = null;
         } else {
            boolean var1 = false;
            int var2 = 0;
            int var3 = 0;
            int var4 = 0;
            int var5 = 1;
            String var6 = "";
            br = br.toUpperCase();

            for(int var7 = 0; var7 < var0; ++var7) {
               char var8;
               if (((var8 = br.charAt(var7)) < ' ' || var8 > 'z') && var8 != 130 && var8 != '\n') {
                  var6 = null;
                  break;
               }

               if (var8 == '\n') {
                  var1 = true;
               } else if (var7 < var0 - 1 && var8 == '\\' && (br.charAt(var7 + 1) == 'n' || br.charAt(var7 + 1) == 'N')) {
                  var1 = true;
                  ++var7;
               }

               if (var1) {
                  if (var6.length() > 0) {
                     ++var5;
                     if (var5 == 3) {
                        var6 = null;
                        break;
                     }

                     var6 = var6 + '\n';
                     var3 = 0;
                  } else {
                     var6 = var6 + "";
                  }

                  var1 = false;
               } else {
                  var6 = var6 + var8;
                  var3 += d(var8, Q);
                  if (var8 == ' ') {
                     var2 = var6.length() - 1;
                     var4 = var3;
                  }
               }

               if (var3 >= bu) {
                  if (var5 >= 3) {
                     var6 = null;
                     break;
                  }

                  if (var2 == var7) {
                     var6 = var6 + "\n";
                     var3 = 0;
                  } else {
                     if (var2 == 0) {
                        var6 = null;
                        break;
                     }

                     var6 = var6.substring(0, var2) + "\n" + var6.substring(var2 + 1, var6.length());
                     var3 -= var4;
                  }

                  ++var5;
               }
            }

            if (var6 != null && !b(var6, 7)) {
               var6 = null;
            }

            br = var6;
         }
      }
   }

   private static void e(int var0) {
      Object var1 = null;
      L = 0;
      int var2 = var0 - 4;
      int var7;
      int var8;
      int var10;
      int var13;
      byte[] var17;
      int var20;
      switch(var0) {
      case -1:
         aA = new Image[H];
         aB = new Image[9];
         aN = new Image[3][];
         aN[0] = new Image[ab.length];
         aN[1] = new Image[ac.length];
         aN[2] = new Image[ad.length];
         return;
      case 0:
         b();
         return;
      case 1:
         var17 = b(var0);

         int var3;
         for(int var4 = 0; var4 < bq; ++var4) {
            var3 = a(var17);
            L += var3;
         }

         a(var17);
         ag = new String[var3 = a(var17)];
         byte[] var18 = new byte[var3];
         System.arraycopy(var17, L, var18, 0, var3);
         L += var3;
         a(var17);
         int var5;
         ah = new short[var5 = var17[L++] & 255 | (var17[L++] & 255) << 8];

         int var19;
         for(var19 = 0; var19 < var5 - 1; ++var19) {
            ah[var19] = (short)((var17[L++] & 255) + ((var17[L++] & 255) << 8));
         }

         ah[var5 - 1] = (short)var3;

         for(var19 = 0; var19 < var5; ++var19) {
            var7 = var19 == 0 ? 0 : ah[var19 - 1] & '\uffff';
            if ((var8 = (ah[var19] & '\uffff') - var7) != 0) {
               try {
                  if (e.i) {
                     ag[var19] = new String(var18, var7, var8, "UTF-8");
                  } else {
                     StringBuffer var9 = new StringBuffer(var8 / 2 + 2);

                     for(var10 = var7; var10 < var7 + var8; ag[var19] = var9.toString().toUpperCase()) {
                        if ((var18[var10] & 128) == 0) {
                           var9.append((char)(var18[var10++] & 255));
                        } else if ((var18[var10] & 224) == 192) {
                           if (var10 + 1 >= var7 + var8 || (var18[var10 + 1] & 192) != 128) {
                              throw new Exception();
                           }

                           var9.append((char)((var18[var10++] & 31) << 6 | var18[var10++] & 63));
                        } else {
                           if ((var18[var10] & 240) != 224) {
                              throw new Exception();
                           }

                           if (var10 + 2 >= var7 + var8 || (var18[var10 + 1] & 192) != 128 || (var18[var10 + 2] & 192) != 128) {
                              throw new Exception();
                           }

                           var9.append((char)((var18[var10++] & 15) << 12 | (var18[var10++] & 63) << 6 | var18[var10++] & 63));
                        }
                     }
                  }
               } catch (Exception var16) {
               }
            }
         }

         if (aq) {
            by = new Command(c(y), 4, 1);
            bz = new Command(c(z), 2, 1);
            a(true, true);
            return;
         }
         break;
      case 2:
         S = new Image[3];
         byte[] var6 = new byte[(var17 = b(var0)).length];
         System.arraycopy(var17, 0, var6, 0, var17.length);
         var7 = a(var17);
         L = 0;
         S[0] = c(var17);
         if (e.g) {
            S[1] = a((byte[])var6, 2, var7, 1, 46319);
            S[2] = a((byte[])var6, 2, var7, 1, 16711680);
         } else {
            S[1] = S[0];
            S[2] = S[0];
         }

         var8 = a(var17);
         M = new byte[(a(var17) + 1) * (4 + bL)];
         var10 = var8 / (6 + bL);

         for(var20 = 0; var20 < var10; ++var20) {
            int var11 = a(var17) * (4 + bL);
            System.arraycopy(var17, L, M, var11, 4 + bL);
            L += 4 + bL;
         }

         N = M[32 * (4 + bL) + R];
         o = N == 13 ? 0 : -1;
         h();
         return;
      case 3:
         var17 = b(var0);

         for(var20 = 0; var20 < H; ++var20) {
            if (e.n || var20 != 13 && var20 != 12) {
               aA[var20] = c(var17);
            }
         }

         if (aE != null) {
            aA[8] = aE;
            aA[9] = aD;
         }

         if (aJ != null) {
            aA[4] = aH;
            aA[5] = aJ;
            aA[6] = aI;
            aA[7] = aK;
         }

         if (aF != null) {
            aA[13] = aG;
            aA[12] = aF;
         }

         if (aM != null) {
            aA[10] = aM;
         }

         if (aM != null) {
            aA[10] = aM;
            return;
         }
         break;
      case 4:
      case 5:
      case 6:
         aB[var2] = c(b(var0));
         if (aL[var2] != null) {
            aB[var2] = aL[var2];
            return;
         }
         break;
      case 7:
         byte[] var12 = b(var17 = b(var0));
         aB[var2] = a(var12, 0, var12.length);

         for(var13 = 0; var13 < bq; ++var13) {
            c(var17);
         }

         aC = c(var17);
         return;
      case 8:
      case 9:
         var13 = (var0 == 8 ? ab.length : ac.length) - 1;
         int var14 = var0 == 8 ? 0 : 1;
         var17 = b(var0);

         int var15;
         for(var15 = 0; var15 < var13; ++var15) {
            aN[var14][var15] = c(var17);
         }

         aN[var14][var15] = aA[11];
         aB[var2] = aA[10];
         return;
      case 10:
         aB[6] = c(b(var0));
         return;
      default:
         if (var0 == q[0]) {
            var17 = b(var0);
            aB[8] = c(var17);
            return;
         }

         if (var0 == r) {
            c();
         }
      }

   }

   private static void a(boolean var0) {
      int var1;
      for(var1 = 0; var1 < aN.length; ++var1) {
         if (aN[var1] != null) {
            for(int var2 = 0; var2 < aN[var1].length; ++var2) {
               aN[var1][var2] = null;
            }
         }
      }

      for(var1 = 0; var1 < aB.length; ++var1) {
         aB[var1] = null;
      }

      aC = null;
      if (var0) {
         c();
         M = null;
         S = null;

         for(var1 = 0; var1 < H; ++var1) {
            aA[var1] = null;
         }

         aA = null;
         ah = null;
         ag = null;
         U = null;
         bs = null;
         aB = null;
         aN = (Image[][])null;
         aD = null;
         aE = null;
         aF = null;
         aG = null;
         aH = null;
         aI = null;
         aJ = null;
         aK = null;
         aM = null;

         for(var1 = 0; var1 < aL.length; ++var1) {
            aL[var1] = null;
         }
      }

      System.gc();
   }

   public static boolean a(int var0) {
      if (!au) {
         return true;
      } else {
         if (var0 == 25) {
            if (e.a) {
               var0 = 0;
            }
         } else if (var0 == 26 && e.b) {
            var0 = 0;
         }

         if (e.n && bB) {
            bB = false;
         } else {
            aw = var0;
         }

         switch(av) {
         case 0:
            if (ay >= az) {
               av = 1;
               i();
            } else {
               e(ay);
            }

            ++ay;
            break;
         case 1:
            switch(aw) {
            case 21:
               if (bk && aW > 0) {
                  --aW;
                  if (aW - aV < 0) {
                     --aV;
                     return false;
                  }
               }

               return false;
            case 22:
            case 28:
            case 29:
            case 30:
            case 31:
            default:
               return false;
            case 23:
               if (bc <= 1) {
                  return false;
               }

               if (ax == 0) {
                  ax = 8;
               } else {
                  --ax;
               }

               while(!be[ax]) {
                  if (ax == 0) {
                     ax = 8;
                  } else {
                     --ax;
                  }
               }

               al = true;
            case 24:
               if (bc > 1) {
                  if (!al) {
                     if (ax == 8) {
                        ax = 0;
                     } else {
                        ++ax;
                     }

                     while(!be[ax]) {
                        if (ax == 8) {
                           ax = 0;
                        } else {
                           ++ax;
                        }
                     }

                     am = true;
                  }

                  aV = 0;
                  aW = 0;
                  i();
               }

               return false;
            case 25:
            case 27:
               av = 6;
               return false;
            case 26:
               av = 4;
               return false;
            case 32:
               if (bk && aW < aT - 1) {
                  ++aW;
                  if (aW - aV >= aU) {
                     ++aV;
                     return false;
                  }
               }

               return false;
            }
         case 2:
            a(false);
            if (e.f == 1) {
               b();
            }

            ay = d(ax);
            e(ay);
            if (e.f == 1) {
               c();
            }

            av = 1;
            break;
         case 3:
            switch(aw) {
            case 25:
            case 27:
               if (e.j == 2) {
                  at = ap;
               } else {
                  h = ap;
               }

               return false;
            case 26:
               av = 1;
               ap = null;
               return false;
            default:
               return false;
            }
         case 4:
            a(true);
            if (aq) {
               ao.setCommandListener(ar);
               k();
            }

            as = false;
            return true;
         case 5:
         default:
            break;
         case 6:
            String var1 = null;
            var1 = bd[ax];
            if (bk) {
               var1 = aO[aS][aW];
            }

            if (var1 != null && var1.length() > 0) {
               if (aa) {
                  int var2;
                  if ((var2 = var1.indexOf("&lg=")) == -1) {
                     var1 = var1 + "&lg=" + e[bq];
                  } else {
                     var1 = var1.substring(0, var2) + "&lg=" + e[bq] + var1.substring(var2 + "&lg=".length() + 2);
                  }
               }

               if (e.j == 2) {
                  at = var1;
               } else {
                  h = var1;
               }
            }
         }

         return false;
      }
   }

   public static void a(int var0, int var1) {
      int var2;
      if ((var2 = e(var0, var1)) != 0) {
         aw = var2;
         bB = true;
      }

      bA = 0;
      bC = false;
   }

   public static void b(int var0, int var1) {
      int var2;
      if ((var2 = e(var0, var1)) != 0) {
         bA = var2;
      }

   }

   public static void c(int var0, int var1) {
      aw = 0;
      bA = 0;
      bC = false;
      b(var0, var1);
   }

   private static int e(int var0, int var1) {
      if (var0 >= 1 && var1 >= 1) {
         if (av != 0 && as) {
            int var3;
            int var4;
            int var5;
            if (bk) {
               int var2 = bv * 5 / 100;
               var3 = 1 + var2 + aA[5].getWidth();
               var4 = bu * 1 / 100;
               var5 = var3 + aN[aS][0].getWidth() + var4;
               boolean var6 = var0 >= aY && var0 <= aY + ba;

               for(int var7 = aV; var7 < aV + aU; ++var7) {
                  if (var6 && var1 > bH + bb * (var7 - aV) && var1 < bH + bb * (var7 - aV) + bb) {
                     if (aW == var7) {
                        return 25;
                     }

                     aW = var7;
                     return 0;
                  }
               }

               if (aU < aT && var6) {
                  if (aW > 0 && var1 > bI - (var5 - 2) / 2 && var1 < bI + (var5 - 2)) {
                     return 21;
                  }

                  if (aW < aT - 1 && var1 > bJ - (var5 - 2) / 2 && var1 < bJ + (var5 - 2)) {
                     return 32;
                  }
               }
            } else {
               if (var0 > bo && var0 < bo + bm && var1 > bp && var1 < bp + bn) {
                  return 27;
               }

               if (e.o && var0 > bD && var0 < bD + bF && var1 > bE && var1 < bE + bG) {
                  return 27;
               }
            }

            var3 = bu - aA[7].getWidth();
            var4 = (bv >> 1) - aA[7].getHeight();
            var5 = aA[7].getHeight() * 2;
            int var8 = aA[7].getWidth() * 2;
            boolean var9 = e.c;
            if (var1 > var4 && var1 < var4 + var5) {
               if (var0 > 0 && var0 < 0 + var8) {
                  return 23;
               }

               if (var0 > var3 - var8 / 2 && var0 < var3 + var8) {
                  return 24;
               }
            }

            if (aq) {
               return 0;
            } else {
               if (var1 >= bv - aA[13].getHeight() * 2 && var1 <= bv) {
                  if (var0 > k && var0 < aA[13].getWidth() * 2 + k) {
                     if (var9) {
                        if (e.a) {
                           return 0;
                        }

                        bC = true;
                        return 25;
                     }

                     if (e.b) {
                        return 0;
                     }

                     return 26;
                  }

                  if (var0 > bu - k - aA[13].getWidth() * 2 && var0 < bu - k) {
                     if (var9) {
                        if (e.b) {
                           return 0;
                        }

                        return 26;
                     }

                     if (e.a) {
                        return 0;
                     }

                     bC = true;
                     return 25;
                  }
               }

               return 0;
            }
         } else {
            return 0;
         }
      } else {
         return 0;
      }
   }

   private static void i() {
      if (e.f == 1) {
         av = 2;
      }

      bg = ax;
      aW = 0;
      aT = 0;
      aV = 0;
      bk = false;
      bh = bd[ax] != null && bd[ax].length() > 0 && bd[ax].compareTo("DEL") != 0;
      if (ax == 4) {
         aS = 0;
         bk = true;
         bh = false;
      }

      if (ax == 5) {
         aS = 1;
         bk = true;
         bh = false;
      }

      if (ax == 6 || ax == 8) {
         bh = true;
      }

      bl = e.n ? A : (e.p ? w : u);
      if (ax == 0 || ax == 1 || ax == 2) {
         bl = e.n ? B : (e.p ? x : v);
      }

      if (bk) {
         aT = aR[aS];
      }

   }

   public static void a(Graphics var0) {
      if (au) {
         if (h != null && e.j == 1) {
            m();
         } else {
            b(var0, 0, 0, bu, bv);
            b(var0, 0, 0, bu, bv);
            switch(av) {
            case 0:
               var0.setColor(0);
               var0.fillRect(0, 0, bu, bv);
               a(var0, bx, bu * 3 / 4, ay, az);
               if (bs != null) {
                  if (bs.trim().equals("")) {
                     return;
                  }

                  var0.setColor(16777215);
                  var0.setFont(i);
                  var0.drawString(bs, bw, bx - 5, 33);
                  return;
               }
               break;
            case 1:
               b(var0);
               if (System.currentTimeMillis() % 1000L <= 500L && (bA != 27 || bC)) {
                  break;
               }

               d(var0);
               return;
            case 2:
               var0.setFont(i);
               int var2 = i.getHeight();
               var0.setColor(255);
               var0.fillRect(0, bx - var2 - 5, bu, var2 * 2);
               var0.setColor(16777215);
               var0.drawString(bs, bw, bx, 65);
               return;
            case 3:
               b(var0);
               int var3 = bv * 40 / 100;
               int var4 = bu;
               if (e.r) {
                  a(var0, 0, var3, var4, bv - var3 * 2, -220209185);
               } else {
                  var0.setColor(14671839);
                  var0.fillRect(0, var3, var4, bv - var3 * 2);
               }

               var0.setColor(39423);
               var0.drawRect(0, var3, var4 - 1, bv - var3 * 2 - 1);
               var0.drawRect(1, var3 + 1, var4 - 3, bv - var3 * 2 - 3);
               a(E, var0, bw, bx, 3);
               e(var0);
            case 4:
            default:
               break;
            case 5:
               return;
            }

         }
      }
   }

   private static void a(Graphics var0, int var1, int var2, int var3, int var4, int var5) {
      int[] var6 = new int[var3 * var4];

      for(int var7 = 0; var7 < var6.length; ++var7) {
         var6[var7] = var5;
      }

      var0.drawRGB(var6, 0, var3, var1, var2, var3, var4, true);
   }

   private static void b(Graphics var0) {
      j();
      var0.setColor(16777215);
      if (p[0]) {
         var0.setColor(0);
      }

      var0.fillRect(0, 0, bu, bv);
      int var1;
      if (bk) {
         c(var0);
      } else {
         int var2;
         int var3;
         if (ax == 8) {
            var1 = bu / 2;
            var2 = bv * 8 / 100;
            a((Graphics)var0, (Image)aB[8], var1, var2, 17);
            X = true;
            a(s, (Graphics)null, 0, 0, 0);
            var3 = V;
            Math.abs((bp - aB[ax].getHeight() - var3) / 3);
            a(c(t), var0, bu - (aA[4].getWidth() + 8) * 2, bu / 2, bv * 60 / 100, 3);
         } else {
            int var5;
            boolean var10;
            if (ax == 3) {
               var10 = bv >= 128;
               boolean var11 = br != null && br.length() > 0;
               var3 = var10 ? aA[10].getHeight() : 0;
               int var4 = l + 3 * var3 / 5 + aB[3].getHeight();
               a((Graphics)var0, (Image)aB[3], bw, var4, 33);
               a((Graphics)var0, (Image)aC, bw, var4, 33);
               if (e.o) {
                  bF = aB[3].getWidth();
                  bG = aB[3].getHeight();
                  bD = bw - 1 - bF / 2;
                  bE = var4 - aB[3].getHeight();
               }

               if (var10) {
                  a((Graphics)var0, (Image)aA[10], bw, l, 17);
               }

               if (var11) {
                  T = 2;
                  var5 = bp - var4;
                  byte var6 = 3;
                  if (N * 2 > var5) {
                     var4 += N / 3;
                     var6 = 17;
                  } else {
                     var4 += var5 / 2;
                  }

                  a(br, var0, bu, bw, var4, var6);
               }
            } else if (ax == 6) {
               var10 = bv >= 128;
               var2 = bv / 2;
               a((Graphics)var0, (Image)aB[ax], bw, var2, 3);
               if (e.o) {
                  bF = aB[ax].getWidth();
                  bF -= 2 * aA[5].getWidth();
                  bG = aB[ax].getHeight();
                  bD = bw - bF / 2;
                  bE = var2 - bG / 2;
               }

               if (var10) {
                  a((Graphics)var0, (Image)aA[10], bw, l, 17);
               }

               X = true;
               a(c(bg), (Graphics)null, 0, 0, 0, 0);
               var2 -= V / 2;
               var3 = T;
               T = 0;
               a(c(bg), var0, aB[ax].getWidth() / 2, bw, var2, 17);
               T = var3;
            } else {
               var1 = aB[ax].getHeight();
               var2 = N * 2;
               var3 = var1 + var2;
               var5 = Math.max(0, bv - var3) / 4;
               int var16;
               int var7 = var16 = (var5 = Math.max(0, var5)) / 2;
               a((Graphics)var0, (Image)aB[ax], bw, var7, 17);
               if (e.o) {
                  bF = aB[ax].getWidth();
                  bF -= 2 * aA[5].getWidth();
                  bG = aB[ax].getHeight();
                  bD = bw - bF / 2;
                  bE = var7;
               }

               var16 += var1 + (e.n ? 0 : var5);
               int var8 = aq ? bu : bu - (aA[9].getWidth() + aA[8].getWidth());
               int var9 = bv - 2 - var16 - (e.n ? aA[12].getHeight() : 0);
               a(c(bg), var0, var8, bw, var16 + var9 / 2, 3);
            }
         }
      }

      e(var0);
      if (bc > 1) {
         var1 = Math.abs((int)(System.currentTimeMillis() / 80L % 8L) - 4);
         byte var12 = 5;
         byte var13 = 7;
         byte var14 = 1;
         byte var15 = 3;
         if (al || e.n && bA == 23) {
            var12 = 4;
            var14 = 0;
            ++ak;
         }

         if (am || e.n && bA == 24) {
            var13 = 6;
            var15 = 2;
            ++ak;
         }

         a((Graphics)var0, (Image)aA[var12], 1 + var1, bx, 6);
         if (!e.n && !e.e) {
            a((Graphics)var0, (Image)aA[var14], 1 + var1 + ai, bx, 6);
         }

         a((Graphics)var0, (Image)aA[var13], bu - 1 - var1, bx, 10);
         if (!e.n && !e.e) {
            a((Graphics)var0, (Image)aA[var15], bu - 1 - var1 - (aA[var13].getWidth() - aA[var15].getWidth()) + aj, bx, 10);
         }

         if (ak > 4) {
            al = false;
            am = false;
            ak = 0;
         }
      }

   }

   private static void c(Graphics var0) {
      int var1 = l;
      if (bv > 128) {
         a((Graphics)var0, (Image)aB[ax], bw, l, 17);
         var1 += aB[ax].getHeight();
      }

      T = 2;
      a(bg, var0, bw, var1, 17);
      int var3 = Math.max(2 * N, aN[aS][aP[aS][0]].getHeight());
      X = true;
      a(bg, (Graphics)null, 0, 0, 0);
      int var4 = var1 + V;
      int var5;
      int var6 = var5 = bv * 7 / 100;
      int var7 = (bv / 2 - var4) * 2;
      int var8 = bv - aA[8].getHeight() - 2 - var4;
      aU = aT;
      if (var7 / var3 >= aT) {
         var4 = var4 + var7 / 2 - aU * var3 / 2;
      } else {
         if (var8 / var3 < aT) {
            aU = (var8 - 2 * var6) / var3;
         }

         var4 = var4 + var8 / 2 - aU * var3 / 2;
      }

      if (aU < aT) {
         if (aV > 0) {
            a(var0, bw, var4 - var6, var5, 16777215, true, false);
            bI = var4 - var6;
         }

         if (aV + aU < aT) {
            a(var0, bw, var4 + aU * var3 + var6, var5, 16777215, true, true);
            bJ = var4 + aU * var3;
         }
      }

      int var9 = var4 + var3 / 2 + 1;
      ba = bu - 3 * aA[5].getWidth();
      aY = (bu >> 1) - (ba >> 1);
      int var10 = bu * 1 / 100;
      int var11;
      int var12 = (var11 = aY + var10) + aN[aS][0].getWidth() + var10;

      for(int var14 = aV; var14 < aV + aU; ++var14) {
         int var15 = aP[aS][var14];
         a((Graphics)var0, (Image)aN[aS][var15], var11, var9, 6);
         if (aS == 2 || var15 == aQ[aS].length - 1) {
            T = 2;
         }

         a((String)c(aQ[aS][var15]), (Graphics)var0, var12, var9, 6);
         var9 += var3;
      }

      aX = aW - aV;
      aZ = var4 + var3 * aX;
      bb = var3;
      bH = var4;
      var0.setColor(16545540);
      var0.drawRect(aY, aZ, ba, bb);
   }

   private static void a(Graphics var0, int var1, int var2, int var3, int var4, boolean var5, boolean var6) {
      int var7 = var6 ? -1 : 1;
      if (var3 % 2 == 0) {
         --var3;
      }

      var0.setColor(46319);
      if (var5) {
         a(var0, var1, var2, var1 - (var3 >> 1), var2 + var7 * (var3 >> 1), var1 + (var3 >> 1), var2 + var7 * (var3 >> 1));
      } else {
         a(var0, var1, var2, var1 - var7 * (var3 >> 1), var2 - (var3 >> 1), var1 - var7 * (var3 >> 1), var2 + (var3 >> 1));
      }

      var0.setColor(var4);
      if (var5) {
         a(var0, var1, var2 + var7, var1 - (var3 >> 1) + 2, var2 + var7 * (var3 >> 1) - var7, var1 + (var3 >> 1) - 2, var2 + var7 * (var3 >> 1) - var7);
      } else {
         a(var0, var1 - var7, var2, var1 - var7 * (var3 >> 1) + var7, var2 - (var3 >> 1) + 2, var1 - var7 * (var3 >> 1) + var7, var2 + (var3 >> 1) - 2);
      }
   }

   private static void j() {
      X = true;
      a(bl, (Graphics)null, 0, 0, 3);
      int var0 = d(32, Q) / 2;
      int var1 = d(32, R) / 3;
      bm = W + var0;
      bn = V + var1;
      if ((V + bn) % 2 != 0) {
         ++bn;
      }

      bo = bw - bm / 2;
      switch(ax) {
      case 0:
      case 1:
      case 2:
      case 8:
         bp = m - bn / 2;
         break;
      case 3:
      case 6:
         bp = n - bn / 2;
      case 4:
      case 5:
      case 7:
      }

      if (p[0] && ax == 8) {
         bp = bv * 80 / 100;
      }

   }

   private static void d(Graphics var0) {
      if (bh) {
         var0.setColor(j);
         if (p[0]) {
            var0.setColor(16744192);
         }

         if (!bC && bA == 27) {
            var0.setColor(14588928);
            var0.drawRect(bo - 2, bp - 2, bm + 3, bn + 4);
            var0.drawRect(bo - 1, bp - 1, bm + 1, bn + 2);
            var0.setColor(5767410);
         }

         T = 0;
         var0.fillRect(bo, bp, bm, bn + 1);
         a(bl, var0, bw, bp + (bn >> 1), 3);
      }

   }

   private static void e(Graphics var0) {
      int var1 = k;
      int var2 = bv - 2;
      int var3 = var1;
      int var4 = bu - var1;
      if (!aq) {
         if (e.n) {
            Image var5 = aA[12];
            Image var6 = aA[12];
            if (bA == 26) {
               if (e.c) {
                  var6 = aA[13];
               } else {
                  var5 = aA[13];
               }
            } else if (bA == 25 && bC) {
               if (e.c) {
                  var5 = aA[13];
               } else {
                  var6 = aA[13];
               }
            }

            if (!e.a) {
               bj = (byte)(bj | (e.c ? 1 : 2));
               bi = (byte)(bi | 1);
            }

            if (!e.b) {
               bj = (byte)(bj | (e.c ? 2 : 1));
               bi = (byte)(bi | 2);
            }

            if ((bj & 1) != 0) {
               a((Graphics)var0, (Image)var5, var1, var2, 36);
            }

            if ((bj & 2) != 0) {
               a((Graphics)var0, (Image)var6, bu - var1, var2, 40);
            }

            var3 = var1 + (aA[12].getWidth() / 2 - aA[9].getWidth() / 2);
            var4 += -aA[12].getWidth() / 2 + aA[8].getWidth() / 2;
            var2 += -aA[12].getHeight() / 2 + aA[9].getHeight() / 2;
         }

         if (!e.a) {
            bi = (byte)(bi | 1);
         }

         if (!e.b) {
            bi = (byte)(bi | 2);
         }

         if (e.c) {
            if ((bi & 1) != 0) {
               a((Graphics)var0, (Image)aA[9], var3, var2, 36);
            }

            if ((bi & 2) != 0) {
               a((Graphics)var0, (Image)aA[8], var4, var2, 40);
               return;
            }
         } else {
            if ((bi & 2) != 0) {
               a((Graphics)var0, (Image)aA[8], var3, var2, 36);
            }

            if ((bi & 1) != 0) {
               a((Graphics)var0, (Image)aA[9], var4, var2, 40);
            }
         }

      }
   }

   private static void a(boolean var0, boolean var1) {
      if (bz != null) {
         ao.removeCommand(bz);
      }

      if (by != null) {
         ao.removeCommand(by);
      }

      if (e.c) {
         if (var0) {
            ao.addCommand(by);
         }

         if (var1) {
            ao.addCommand(bz);
            return;
         }
      } else {
         if (var1) {
            ao.addCommand(bz);
         }

         if (var0) {
            ao.addCommand(by);
         }
      }

   }

   private static void k() {
      if (bz != null) {
         ao.removeCommand(bz);
         bz = null;
      }

      if (by != null) {
         ao.removeCommand(by);
         by = null;
      }

   }

   private static Image a(byte[] var0, int var1, int var2, int var3, int var4) {
      int var5 = 0;

      for(int var6 = var1; var5 == 0 && var6 < var1 + var2; ++var6) {
         if ((var0[var6] & 255) == 80 && (var0[var6 + 1] & 255) == 76 && (var0[var6 + 2] & 255) == 84 && (var0[var6 + 3] & 255) == 69) {
            var5 = var6;
         }
      }

      int var9 = (var0[var5 - 4] << 24 & -16777216) + (var0[var5 - 3] << 16 & 16711680) + (var0[var5 - 2] << 8 & '\uff00') + (var0[var5 - 1] << 0 & 255);
      var0[var5 + 4 + 3 * var3] = (byte)((var4 & 16711680) >> 16);
      var0[var5 + 4 + 3 * var3 + 1] = (byte)((var4 & '\uff00') >> 8);
      var0[var5 + 4 + 3 * var3 + 2] = (byte)(var4 & 255);
      byte[] var10 = new byte[var9 + 4];
      System.arraycopy(var0, var5, var10, 0, var9 + 4);
      long[] var11 = new long[256];

      long var7;
      int var12;
      for(var12 = 0; var12 < 256; ++var12) {
         var7 = (long)var12;

         for(int var13 = 0; var13 < 8; ++var13) {
            if ((var7 & 1L) == 1L) {
               var7 = 3988292384L ^ var7 >> 1;
            } else {
               var7 >>= 1;
            }
         }

         var11[var12] = var7;
      }

      var7 = 4294967295L;

      for(var12 = 0; var12 < var10.length; ++var12) {
         var7 = var11[(int)(var7 ^ (long)var10[var12]) & 255] ^ var7 >> 8;
      }

      var7 ^= 4294967295L;
      var0[var5 + 4 + var9] = (byte)((int)((var7 & -16777216L) >> 24));
      var0[var5 + 4 + var9 + 1] = (byte)((int)((var7 & 16711680L) >> 16));
      var0[var5 + 4 + var9 + 2] = (byte)((int)((var7 & 65280L) >> 8));
      var0[var5 + 4 + var9 + 3] = (byte)((int)((var7 & 255L) >> 0));
      System.gc();
      return a(var0, var1, var2);
   }

   private static void a(Graphics var0, int var1, int var2, int var3, int var4) {
      if (var3 > var4) {
         var3 = var4;
      }

      int var5 = (bu - var2) / 2;
      var0.setColor(16777215);
      var0.drawRect(var5, var1, var2, 6);
      int var6 = (var2 - 2 - 2) * var3 / var4 + 1;
      var0.setColor(16711680);
      var0.fillRect(var5 + 1 + 1, var1 + 1 + 1, var6, 3);
   }

   private static void a(Graphics var0, int var1, int var2, int var3, int var4, int var5, int var6) {
      var0.fillTriangle(var1, var2, var3, var4, var5, var6);
   }

   private static void a(Graphics var0, Image var1, int var2, int var3, int var4, int var5, int var6, int var7) {
      var0.drawRegion(var1, var2, var3, var4, var5, 0, var6, var7, 20);
   }

   private static void a(Graphics var0, Image var1, int var2, int var3, int var4) {
      if (e.q) {
         var0.drawImage(var1, var2, var3, var4);
      } else {
         if ((var4 & 1) != 0) {
            var2 -= var1.getWidth() >> 1;
         }

         if ((var4 & 2) != 0) {
            var3 -= var1.getHeight() >> 1;
         }

         if ((var4 & 8) != 0) {
            var2 -= var1.getWidth();
         }

         if ((var4 & 32) != 0) {
            var3 -= var1.getHeight();
         }

         var0.drawImage(var1, var2, var3, 0);
      }
   }

   private static void b(Graphics var0, int var1, int var2, int var3, int var4) {
      var1 = Math.max(var1, 0);
      var2 = Math.max(var2, 0);
      var3 = Math.min(var3, bu);
      var4 = Math.min(var4, bv);
      var0.setClip(var1, var2, var3, var4);
   }

   public final void run() {
      if (e.j == 2) {
         while(as) {
            try {
               if (at != null) {
                  h = at;
                  m();
                  at = null;
               }

               Thread.sleep(1000L);
            } catch (Exception var2) {
            }
         }
      }

   }

   private static void l() {
      RecordStore var0 = null;

      try {
         var0 = RecordStore.openRecordStore("igp19", false);
      } catch (Exception var5) {
         try {
            var0 = RecordStore.openRecordStore("igp19", true);
         } catch (Exception var4) {
         }
      }

      try {
         if (var0 != null) {
            var0.closeRecordStore();
         }

      } catch (Exception var3) {
      }
   }

   private static void m() {
      if (h != null && h.length() > 0) {
         String var0 = h;
         h = null;
         (new StringBuffer()).append("urlPlatformRequest = ").append(var0).toString();

         try {
            b(var0);
            if (e.k) {
               Thread.sleep(2000L);
            } else {
               Thread.sleep(200L);
            }
         } catch (Exception var2) {
         }

         if (e.m) {
            av = 4;
         } else {
            av = 1;
         }

         if (e.l) {
            an.notifyDestroyed();
         }
      }

   }

   public final void commandAction(Command var1, Displayable var2) {
      if (aq) {
         if (var1 == by) {
            a(25);
            return;
         }

         if (var1 == bz) {
            a(26);
         }
      }

   }

   private static final String a(String var0) {
      if (e.s) {
         String var1;
         return (var1 = (String)bK.get(var0)) != null && var1 != "" ? var1 : null;
      } else {
         return an.getAppProperty(var0);
      }
   }

   private static final void b(String var0) throws ConnectionNotFoundException {
      an.platformRequest(var0);
   }

   private static final void n() {
      bK.put("URL-TEMPLATE-GAME", e.t);
      bK.put("URL-OPERATOR", e.u);
      bK.put("URL-PT", e.v);
      bK.put("IGP-PROMOS", e.w);
      bK.put("IGP-WN", e.x);
      bK.put("IGP-BS", e.y);
      bK.put("IGP-CATEGORIES", e.z);
      bK.put("IGP-VERSION", e.A);
      bK.put("URL-ORANGE", e.B);
   }

   static {
      b = "IGP-Signature=" + a;
      g = "";
      j = 13568256;
      k = e.d;
      o = 0;
      p = new boolean[1];
      q = new int[1];
      c = -1;
      d = 10;
      H = 14;
      O = 0;
      P = 1;
      Q = 2;
      R = 3;
      Y = new String[]{"URL-WN", "URL-BS", "URL"};
      e = new String[0];
      ai = 6;
      aj = 3;
      aq = false;
      ar = null;
      f = null;
      as = false;
      at = null;
      au = false;
      bc = -1;
      bf = 0;
      bA = 0;
      bB = false;
      bC = false;
      bD = -1;
      bE = -1;
      bF = -1;
      bG = -1;
      bK = new Hashtable();
      bL = 0;
   }
}
