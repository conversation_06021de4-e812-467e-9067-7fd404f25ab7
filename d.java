import javax.microedition.lcdui.Graphics;

final class d {
   int a;
   int b;
   int c;
   a d;
   int e;
   int f;
   int g;
   int[] h;
   d i;

   d() {
   }

   d(a var1, int var2, int var3, d var4) {
      this.a = var2 << 8;
      this.b = var3 << 8;
      this.d = var1;
      this.h = new int[4];
      this.i = var4;
   }

   final void a(int var1) {
      if (var1 != this.e) {
         this.e = var1;
         this.f = 0;
         this.g = 0;
      }

   }

   final boolean a() {
      if (this.e < 0) {
         return true;
      } else if (this.f != (this.d.h[this.e] & 255) - 1) {
         return false;
      } else {
         int var1;
         return (var1 = this.d.a(this.e, this.f)) == 0 || this.g == var1 - 1;
      }
   }

   final int b(int var1) {
      int var2 = 0;
      int var4 = this.d.h[var1] & 255;

      for(int var3 = 0; var3 < var4; ++var3) {
         var2 += this.d.a(var1, var3);
      }

      return var2;
   }

   final void a(Graphics var1) {
      if (this.d != null) {
         int var2 = this.a;
         int var3 = this.b;

         for(d var4 = this.i; var4 != null; var4 = var4.i) {
            var2 += var4.a;
            var3 += var4.b;
         }

         var2 = d(var2) + 0;
         var3 = e(var3) + 0;
         if (this.g >= 0) {
            this.d.a((Graphics)var1, this.e, this.f, var2, var3, this.c, 0, 0);
         } else if (this.e >= 0) {
            this.d.a(var1, this.e, var2, var3, this.c);
         } else {
            if (this.f >= 0) {
               this.d.a((Graphics)var1, this.f, var2, var3, this.c, 0, 0);
            }

         }
      }
   }

   final int[] c(int var1) {
      int[] var2;
      (var2 = new int[2])[0] = this.a >> 8;
      var2[1] = this.b >> 8;
      return this.d.a(this.e, this.f, var2, this.c, var1);
   }

   final void b() {
      if (this.d != null) {
         if (this.g >= 0) {
            int var1;
            if ((var1 = this.d.a(this.e, this.f)) != 0) {
               ++this.g;
               if (var1 <= this.g) {
                  this.g = 0;
                  ++this.f;
                  if (this.f >= (this.d.h[this.e] & 255)) {
                     this.f = 0;
                  }

               }
            }
         }
      }
   }

   final int[] c() {
      if (this.h == null) {
         this.h = new int[4];
      }

      if (this.d != null) {
         if (this.g >= 0) {
            this.d.a((int[])this.h, this.e, this.f, this.a, this.b, this.c, 0, 0);
         } else if (this.e >= 0) {
            this.d.a(this.h, this.e, this.a, this.b);
         } else if (this.f >= 0) {
            this.d.a((int[])this.h, this.f, this.a, this.b, this.c, 0, 0);
         }
      }

      return this.h;
   }

   private static int d(int var0) {
      return (var0 >> 8) * 1 / 1;
   }

   private static int e(int var0) {
      return (var0 >> 8) * 1 / 1;
   }
}
