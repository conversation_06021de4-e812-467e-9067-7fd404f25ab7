import java.util.Vector;
import javax.microedition.lcdui.Graphics;
import javax.microedition.lcdui.Image;
import javax.microedition.media.Manager;
import javax.microedition.media.Player;

public final class f {
   static int a;
   static a[] b;
   static d[] c;
   static boolean d = false;
   static short[][] e;
   static short f;
   static Graphics g;
   static byte[] h;
   static byte[] i;
   static byte[][] j;
   static short[] k;
   static byte[] l;
   static Image m;
   static int n;
   static int o;
   static byte[] p = null;
   static byte[] q = null;
   static byte r = -1;
   static byte s = -1;
   static int t;
   static int u;
   static int v;
   static int[] w = new int[]{128, 128};
   static int[] x = new int[]{-128, 128};
   static int y;
   static int z;
   static int A;
   static int B;
   static int C;
   static int D;
   static int E = -10000;
   static int F = -10000;
   static byte[] G = null;
   static short[] H = null;
   static byte[] I = null;
   static boolean J;
   static byte K;
   static int[][] L = new int[][]{{0, 7}, {0, 7}, {22, 7}, {0, 3}, {0, 3}, {-7, 7}, {0, -82}, {0, 7}, {0, -82}};
   static int[] M = new int[]{0, 0};
   static int[] N = new int[]{0, 0};
   static int[] O = new int[]{0, 0};
   static int[] P = new int[]{0, 0};
   static int[] Q = new int[32];
   static int[] R = new int[32];
   static int S;
   static int T;
   static int U;
   static int V;
   static byte[] W = new byte[64];
   static int X;
   static byte[] Y;
   static byte[] Z;
   static byte[] aa;
   static byte[] ab;
   static byte[] ac;
   static byte[] ad;
   static byte[] ae;
   static byte[] af;
   static int[][] ag;
   static boolean ah;
   static int ai = -1;
   static Graphics aj;
   static int[] ak;
   static byte al;
   static int am;
   static byte[] an = null;
   static int[] ao = null;
   static long[] ap = null;
   static boolean aq;
   static boolean ar;
   static int as;
   static byte[] at;
   static int au;
   static int av;
   static int aw;
   static byte ax = -1;
   static byte ay;
   static int az;
   static int aA;
   static long aB;
   static byte aC;
   static boolean aD;
   static int aE;
   static d aF;
   static byte aG;
   static int aH;
   static int[] aI = new int[]{0, 0};
   static byte[] aJ;
   static byte[] aK;
   static byte[] aL;
   static int aM;
   static int aN;
   static int aO;
   static int aP;
   static int aQ;
   static int aR;
   static Vector aS = new Vector();
   static long aT;
   static long aU;
   static int aV;
   static String[] aW;
   static int[] aX;
   static int aY;
   static d[] aZ;
   static boolean ba;
   static byte[] bb;
   static byte[] bc;
   static int bd;
   static String be = null;
   static byte[] bf;
   static byte bg = -1;
   static byte bh;
   static int bi;
   static int bj;
   static boolean bk = false;
   static int bl;
   static byte bm = -1;
   static boolean bn = false;
   static int[] bo;
   public static Player bp = null;

   static void a(boolean var0) {
      GloftMMN.a("LEVELS_INIT START");
      b = new a[98];

      try {
         GloftMMN.b("/8");
         h.a(0, b, 2, false, false, -1, false);
         GloftMMN.h(1);
         GloftMMN.a("LEVELS_INIT AFTER OPEN PACK");
         int var2;
         h = new byte[var2 = GloftMMN.g() * 90];
         GloftMMN.a(h, 0, var2);
         GloftMMN.a("LEVELS_INIT AFTER BG SETS");
         GloftMMN.h(3);
         i = new byte[GloftMMN.g() * 3];
         GloftMMN.a(i, 0, i.length);
         GloftMMN.a("LEVELS_INIT AFTER MAPS");
         GloftMMN.h(4);
         ak = new int[18];

         for(int var1 = 2; var1 < 18; ++var1) {
            int var3 = GloftMMN.g();
            int var4 = GloftMMN.g();
            int var5 = GloftMMN.g();
            ak[var1] = -16777216 | var3 << 16 | var4 << 8 | var5;
         }

         ak[0] = -1;
         ak[1] = -1;
         q();
         GloftMMN.a("LEVELS_INIT AFTER OBJECT SIZES & daytimeColors");
         if (var0) {
            GloftMMN.f();
            GloftMMN.a("LEVELS_INIT END");
         }

      } catch (Exception var6) {
      }
   }

   static void b(boolean var0) {
      if (var0) {
         h.u = new a[6];
         h.v = new d[20];
         GloftMMN.b("/10");
         h.a(3, h.u, 3, true, true, 1, true);
         h.a(0, h.u, 0, true, true, 1, true);
         h.a(1, h.u, 1, true, true, 1, false);
         h.a(2, h.u, 2, true, true, 1, false);
         GloftMMN.f();
         h.v[7] = new d(h.u[3], 0, 0, (d)null);
         h.v[0] = new d(h.u[0], 0, 0, (d)null);
         h.v[3] = new d(h.u[0], 0, 0, (d)null);
         h.v[4] = new d(h.u[0], 0, 0, (d)null);
         h.v[1] = new d(h.u[0], 0, 0, (d)null);
         h.v[2] = new d(h.u[0], 0, 0, (d)null);
         h.v[17] = new d(h.u[1], 0, 0, (d)null);
         h.v[17].e = -1;
         h.v[18] = new d(h.u[1], 0, 0, (d)null);
         h.v[18].e = -1;
         h.v[19] = new d(h.u[1], 0, 0, (d)null);
         h.v[9] = new d(h.u[3], 0, 0, (d)null);
         h.v[10] = new d(h.u[3], 0, 0, (d)null);
         h.v[5] = new d(h.u[3], 0, 0, (d)null);
         h.v[6] = new d(h.u[3], 0, 0, h.v[5]);
         h.v[8] = new d(h.u[3], 120, 200, (d)null);
         h.v[8].a(8);
         h.v[11] = new d(h.u[1], 0, 0, (d)null);
         h.v[12] = new d(h.u[1], 0, 0, (d)null);
      } else {
         int var1;
         for(var1 = 0; var1 < h.v.length; ++var1) {
            h.v[var1] = null;
         }

         for(var1 = 0; var1 < h.u.length; ++var1) {
            h.u[var1] = null;
         }

         if (h.s != null) {
            for(var1 = 0; var1 < h.s.length; ++var1) {
               if (h.s[var1] != null) {
                  h.s[var1].y = null;
                  h.s[var1].z = null;
               }
            }
         }

         g.S = null;
         g.T = null;
         aZ = null;
         h.u = null;
         h.v = null;
      }
   }

   static void a() {
      GloftMMN.b("/2");
      h.M = new a();
      h.M.a(GloftMMN.j(0), 0, 2, false);
      GloftMMN.h(1);
      byte[] var1;
      GloftMMN.a(var1 = new byte[GloftMMN.i[1]]);
      GloftMMN.f();
      h.M.a(1, 0, -1, -1, 0);
      h.M.a(0, 0, -1, -1, 1);
      h.M.b();
      h.M.A = new byte[256];

      int var0;
      for(var0 = 65; var0 <= 90; ++var0) {
         h.M.A[var0] = (byte)(var0 - 65 + 3);
      }

      for(var0 = 48; var0 <= 57; ++var0) {
         h.M.A[var0] = (byte)(var0 - 48 + 26 + 3);
      }

      for(var0 = 0; var0 < var1.length; ++var0) {
         h.M.A[var1[var0] & 255] = (byte)(39 + var0);
      }

      h.M.A[209] = 2;
      h.M.c(2);
   }

   private static void a(int var0, a var1) {
      try {
         GloftMMN.h(var0);
         int var4 = GloftMMN.g();
         GloftMMN.g();
         int var5 = var1.l;

         for(int var2 = 0; var2 < var5; ++var2) {
            var1.k[var2] = new int[var4];

            for(int var3 = 0; var3 < var4; ++var3) {
               var1.k[var2][var3] = a(GloftMMN.h());
            }
         }

      } catch (Exception var6) {
      }
   }

   static void b() {
      k = null;
      l = null;
      int var0;
      if (c != null) {
         for(var0 = 0; var0 < c.length; ++var0) {
            c[var0] = null;
         }
      }

      c = null;
      GloftMMN.a("AFTER CLEAR ALL OBJECT SPRITE INSTANCES");
      if (b != null) {
         for(var0 = 3; var0 < b.length; ++var0) {
            b[var0] = null;
         }

         if (b[0] != null) {
            b[0].a();
         }

         if (b[1] != null) {
            b[1].a();
         }

         if (b[2] != null) {
            b[2].a();
         }
      }

      GloftMMN.a("AFTER CLEAR ALL OBJECT SPRITES");
      Y = null;
      Z = null;
      aa = null;
      ab = null;
      ac = null;
      ad = null;
      ae = null;
      af = null;
      ag = (int[][])null;
      aF = null;
      e = (short[][])null;
      k = null;
      l = null;
      p = null;
      q = null;
      GloftMMN.a("AFTER CLEAR ALL MISC ARRAYS");
   }

   static boolean a(Graphics var0, int var1) {
      GloftMMN.a("LLOAD STEP " + bd);
      if (h.k) {
         bd = 0;
      }

      g var7 = h.s[0];
      boolean var8 = h.h == 18;
      int var3;
      int var4;
      int var9;
      short[] var10;
      int var11;
      int var17;
      int var18;
      int var20;
      boolean var21;
      int var24;
      int var26;
      short var27;
      short var28;
      int var35;
      int var37;
      int var38;
      int var45;
      int var46;
      int var54;
      int var56;
      int var57;
      int var61;
      int var69;
      label1363:
      switch(bd) {
      case 0:
         GloftMMN.b();
         if (a > 0) {
            GloftMMN.b(5);
            a = -1;
            GloftMMN.a("CLEAN AFTER UNLOAD SOUND");
         }

         if (h.k) {
            GloftMMN.a("/3", 0);
            GloftMMN.a("RELOADING DATA BECAUSE INTERUPTED !!!");
            a();
            b(true);
         }

         g.N.removeAllElements();
         if (h.J) {
            m = null;
            aj = null;
            GloftMMN.a("CLEAN AFTER BB FREE");
         }

         var0.setColor(0);
         var0.fillRect(0, 0, 240, 400);
         GloftMMN.a("CLEAN BEFORE LOADING PNG OPENING");
         if (!var8) {
            GloftMMN.b("/1");
            byte[] var41 = GloftMMN.j(1);
            GloftMMN.f();
            h.Q = Image.createImage(var41, 0, var41.length);
         }

         GloftMMN.a("AFTER LOADING PNG OPENING");
         d var42;
         (var42 = h.v[7]).a = 30720;
         var42.b = 51200;
         var42.a(47);
         var42.a(var0);
         h.Q = null;
         GloftMMN.a("AFTER LOAD IMAGE DISALLOCATED ! cGame.m_MapImage=" + h.Q);
         h.d();
         break;
      case 1:
         if (bc[26] > 0 && !var8 && (bc[0] >= 30 && var1 != 22 && var1 != 34 && var1 != 35 && var1 != 23 || bc[0] < 30 && var1 != 0 && var1 != 1 && var1 != 2 && var1 != 5)) {
            g var39;
            (var39 = h.s[bc[26]]).s = false;
            g.a(GloftMMN.k(888) + GloftMMN.k(var39.a[1]) + GloftMMN.k(889));
            var39.b[2] = var39.b[4];
            var39.b[3] = 0;
            var39.b[4] = -1;
            bc[26] = -1;
         }

         bc[7] = (byte)var1;
         K = (byte)var1;
         GloftMMN.a("BEFORE TXT FREE");
         if (h.J) {
            GloftMMN.n = null;
            GloftMMN.o = null;
         }

         GloftMMN.a("CLEAN AFTER TXT FREE");
         GloftMMN.a("BEFORE FONT FREE " + GloftMMN.g);
         h.M = null;
         GloftMMN.a("CLEAN AFTER FONT FREE");
         break;
      case 2:
         if (!var8) {
            GloftMMN.a("BEFORE IFACE FREE " + GloftMMN.g);
            b(false);
            GloftMMN.a("CLEAN AFTER IFACE FREE " + GloftMMN.g);
         }

         ai = -1;
         h.au = false;
         if (h.X != 0) {
            g.e();
            g.b(true);

            for(var3 = 0; var3 < g.G.length; ++var3) {
               g.G[var3] = 0;
            }
         }

         aW = new String[4];
         break;
      case 3:
         e();
         break;
      case 4:
         b();
         p = null;
         q = null;
         j = (byte[][])null;
         break;
      case 5:
         var3 = 0;

         while(true) {
            if (var3 >= 5) {
               break label1363;
            }

            GloftMMN.a("AFTER GC " + var3);
            ++var3;
         }
      case 6:
         GloftMMN.a("CLEAN END");
         aU = 100L;
         var35 = 3 * var1;
         y = i[var35 + 0];
         z = i[var35 + 1];
         A = i[var35 + 2];
         GloftMMN.b("/8");
         GloftMMN.h(2);
         j = new byte[GloftMMN.g()][];

         int var40;
         for(var4 = 0; var4 <= z; ++var4) {
            var9 = GloftMMN.g();
            var40 = GloftMMN.g();
            var11 = var9 * var40 >> 1;
            if (var4 != y && var4 != z) {
               GloftMMN.i(var11);
            } else {
               byte var43 = 0;
               j[var4] = new byte[var9 * var40 + 2];
               byte[] var74 = j[var4];
               int var44 = var43 + 1;
               var74[0] = (byte)var9;
               var74 = j[var4];
               ++var44;
               var74[1] = (byte)var40;

               for(var45 = 0; var45 < var11; ++var45) {
                  var46 = GloftMMN.g();
                  j[var4][var44++] = (byte)(var46 >> 4 & 15);
                  j[var4][var44++] = (byte)(var46 & 15);
               }
            }
         }

         GloftMMN.a("LEVELS_INIT AFTER LAYERS");
         GloftMMN.f();
         p = j[y];
         q = j[z];
         r = p[0];
         s = p[1];
         B = A * 90;
         var9 = j[y][0] & 255;
         var40 = j[y][1] & 255;
         t = var9;
         u = var40;
         v = (var40 * w[1] + var9 * x[1]) * 22 >> 8;
         v += 65;
         break;
      case 7:
         GloftMMN.b("/13");
         GloftMMN.h(0 + var1);
         e = new short[140][];

         for(f = 0; (var9 = GloftMMN.g()) != 0; ++f) {
            e[f] = new short[var9];

            for(var4 = 0; var4 < var9; ++var4) {
               e[f][var4] = (short)(GloftMMN.g() | GloftMMN.g() << 8);
            }

            e[f][1] = (short)(e[f][1] * 136 / 100);
            e[f][2] = (short)(e[f][2] * 136 / 100);
         }

         GloftMMN.f();
         var9 = a(95, -1);
         var10 = e[var9];

         for(var3 = 0; var3 < 8; ++var3) {
            bb[var3] = (byte)var10[var3 + 4];
         }

         var9 = a(96, -1);
         var10 = e[var9];

         for(var3 = 8; var3 < 16; ++var3) {
            bb[var3] = (byte)var10[var3 - 8 + 3];
         }

         if ((var11 = a(91, -1)) >= 0) {
            a(var11, h.s[0].c);
         }

         if (bg >= 0 && bg != 14) {
            for(var3 = 0; var3 < f; ++var3) {
               if (e[var3][0] == 94 && e[var3][4] == bg) {
                  a(var3, h.s[0].c);
                  break label1363;
               }
            }
         }
         break;
      case 8:
         if (ah) {
            for(var3 = 0; var3 < 32; ++var3) {
               bc[var3] = h.aw[70 + var3];
            }

            for(var3 = 0; var3 < 16; ++var3) {
               bb[var3] = h.aw[102 + var3];
            }

            var3 = 0;

            while(true) {
               if (var3 >= g.D) {
                  h.n[0].d = h.o[var7.a[0]];
                  var7.c[0] = h.a(h.aw, 49);
                  var7.c[1] = h.a(h.aw, 53);
                  var7.a[35] = h.a(h.aw, 57);
                  aT = h.b(h.aw, 62);

                  for(var3 = 0; var3 < g.ah.length; ++var3) {
                     g.ah[var3] = h.aw[118 + var3];
                     g.f[var3] = h.aw[157 + var3];
                  }

                  for(var3 = 0; var3 < g.ai.length; ++var3) {
                     g.ai[var3] = h.aw[196 + var3] == 1;
                  }

                  for(var3 = 0; var3 < 16; ++var3) {
                     g.ag[var3] = h.b(h.aw, 235 + var3 * 8);
                  }

                  for(var3 = 0; var3 < var7.an.length; ++var3) {
                     var7.an[var3] = h.aw[455 + var3];
                  }

                  for(var3 = 0; var3 < ap.length; ++var3) {
                     ap[var3] = h.b(h.aw, 495 + var3 * 8);
                  }

                  for(var3 = 0; var3 < 25; ++var3) {
                     g.G[var3] = h.aw[1007 + var3] & 255;
                  }

                  for(var3 = 0; var3 < 168; ++var3) {
                     g.I[var3] = h.aw[1032 + var3] & 255;
                  }

                  g.ae = h.a(h.aw, 1200);
                  g.ad = "";

                  for(var3 = 0; var3 < 200 && h.aw[1204 + var3] != 0; ++var3) {
                     g.ad = g.ad + String.valueOf((char)(h.aw[1204 + var3] & 255));
                  }

                  g.aa = h.aw[1404];

                  for(var3 = 0; var3 < 252; ++var3) {
                     h.e[var3] = h.aw[1405 + var3];
                  }

                  for(var3 = 0; var3 < 64; ++var3) {
                     h.f[var3] = h.aw[1657 + var3];
                  }

                  for(var3 = 0; var3 < 16; ++var3) {
                     h.bM[var3] = h.aw[1721 + var3];
                  }

                  for(var3 = 0; var3 < 10 && h.aw[1737 + var3] != -127; ++var3) {
                  }

                  h.af = new String(h.aw, 1737, var3);

                  for(var3 = 1; var3 < h.s.length; ++var3) {
                     h.s[var3] = null;
                  }

                  for(var3 = 1; var3 < h.n.length; ++var3) {
                     h.n[var3] = null;
                  }

                  for(var3 = 0; var3 < 25; ++var3) {
                     var35 = 1747 + var3 * 89;
                     byte var49;
                     if ((var49 = h.aw[var35 + 0]) > 0) {
                        h.s[var49] = null;
                        System.gc();
                        h.s[var49] = new g(-1, -1, -1, true, true);
                        g var53 = h.s[var49];
                        var54 = 0;

                        for(var4 = 0; var4 < 6; ++var4) {
                           if (h.aw[var35 + 1 + var4] != -1) {
                              ++var54;
                           }
                        }

                        if (var54 > 0) {
                           var53.k = new byte[var54];

                           for(var4 = 0; var4 < var54; ++var4) {
                              var53.k[var4] = h.aw[var35 + 1 + var4];
                           }
                        }

                        var54 = 0;

                        for(var4 = 0; var4 < 6; ++var4) {
                           if (h.aw[var35 + 7 + var4] != -1) {
                              ++var54;
                           }
                        }

                        if (var54 > 0) {
                           var53.l = new byte[var54];

                           for(var4 = 0; var4 < var54; ++var4) {
                              var53.l[var4] = h.aw[var35 + 7 + var4];
                           }
                        }

                        var54 = 0;

                        for(var4 = 0; var4 < 6; ++var4) {
                           if (h.aw[var35 + 13 + var4] != -1) {
                              ++var54;
                           }
                        }

                        if (var54 > 0) {
                           var53.m = new byte[var54];

                           for(var4 = 0; var4 < var54; ++var4) {
                              var53.m[var4] = h.aw[var35 + 13 + var4];
                           }
                        }

                        for(var4 = 0; var4 < 46; ++var4) {
                           var53.a[var4] = h.aw[var35 + 19 + var4];
                        }

                        int[] var73 = var53.a;
                        var73[1] += 402;

                        for(var4 = 0; var4 < 21; ++var4) {
                           var53.b[var4] = h.aw[var35 + 65 + var4];
                        }

                        var53.b[18] = -1;
                        var53.b[3] = 0;
                        var53.e = new byte[3];

                        for(var4 = 0; var4 < 3; ++var4) {
                           var53.e[var4] = h.aw[var35 + 86 + var4];
                        }

                        for(var4 = 0; var4 < h.n.length; ++var4) {
                           if (h.n[var4] == null) {
                              var56 = ((var20 = 7 + var53.a[0] * 2) - 7) / 2;
                              var53.b[5] = (byte)var4;
                              h.n[var4] = new d(h.o[var56], 0, 0, (d)null);
                              if (var4 == 0) {
                                 h.t[0] = new d(h.o[var56], 0, 0, (d)null);
                              }
                              break;
                           }
                        }
                     }

                     g.af = aT;
                  }

                  h.N[0] = -1;
                  h.N[1] = -1;
                  h.N[2] = 1;
                  h.N[3] = 0;
                  h.N[4] = 0;
                  h.N[5] = 0;
                  h.N[6] = 0;
                  break;
               }

               if (var3 >= 4 && var3 <= 11 && (var3 - 4) % 2 == 1) {
                  var7.a[var3] = h.aw[2 + var3] & 255;
               } else {
                  var7.a[var3] = h.aw[2 + var3];
               }

               ++var3;
            }
         }

         J = bb[13] == 1;
         var18 = J ? 1 : 0;
         byte var62;
         if (j()) {
            var62 = 3;
         } else if (bb[15] == 1) {
            var62 = 3;
         } else {
            var62 = 1;
         }

         j(var18, var62);
         var38 = 0;

         while(true) {
            if (var38 >= s) {
               break label1363;
            }

            for(var37 = 0; var37 < r; ++var37) {
               var20 = 2 + var37 + var38 * r;
               var56 = 0;
               var57 = 0;
               var61 = 0;
               if (var37 < r - 1) {
                  var57 = p[var20 + 1] & 15;
               }

               if (var37 > 0) {
                  var56 = p[var20 - 1] & 15;
               }

               if (var38 < s - 1) {
                  var61 = p[var20 + r] & 15;
               }

               var24 = p[var20] & 15;
               byte var64 = -1;
               if (var24 != 0) {
                  if ((var26 = h[B + (var24 - 1) * 3 + 2] & 255) == 1) {
                     if (var61 == 0) {
                        var64 = 5;
                     } else {
                        var64 = 6;
                     }

                     if (var37 > 0 && (var17 = p[var20 - 1] & 15) > 0 && (h[B + (var17 - 1) * 3 + 2] & 255) == 2) {
                        var64 = -1;
                     }
                  } else if (var26 == 2) {
                     if (var57 == 0) {
                        var64 = 7;
                     } else {
                        var64 = 8;
                     }

                     if (var37 < r - 1 && (var17 = p[var20 + 1] & 15) > 0 && (h[B + (var17 - 1) * 3 + 2] & 255) == 1) {
                        var64 = -1;
                     }
                  } else if (var24 < 14) {
                     if ((var69 = k(var37, var38)) == 2) {
                        var64 = 0;
                     } else if (var69 == 3) {
                        if (var57 == 0 && var56 != 0) {
                           var64 = 2;
                        } else {
                           var64 = 1;
                        }
                     }
                  } else if (var24 == 14) {
                     var64 = 4;
                  } else if (var24 == 15) {
                     var64 = 3;
                  }
               }

               int var70 = var64 + 1;
               p[var20] = (byte)(var24 | var70 << 4);
            }

            ++var38;
         }
      case 9:
         if (ah) {
            bc[2] = h.aw[72];
         }

         c = new d[f];
         byte[] var12 = new byte[90];

         int var47;
         for(var3 = 0; var3 < f; ++var3) {
            short var13;
            if ((var13 = e[var3][0]) < 90) {
               if ((var47 = e[var3][9] - 2) >= 0 && !g.a(var47, (g)null, false)) {
                  e[var3][5] = -127;
               } else {
                  var12[var13] = 1;
               }

               if (var13 == 55) {
                  var12[var13] = 0;
                  e[var3][5] = -127;
               }
            }
         }

         var45 = -1;
         var46 = 0;

         while(true) {
            if (var46 >= 4) {
               break label1363;
            }

            if (var46 == 0) {
               GloftMMN.b("/14");
            } else if (var46 == 1) {
               GloftMMN.b("/15");
            } else if (var46 == 2) {
               GloftMMN.b("/16");
            } else {
               GloftMMN.b("/17");
            }

            if (var46 == 0) {
               var45 = GloftMMN.h;
            }

            for(var3 = 0; var3 < GloftMMN.h; ++var3) {
               if ((var47 = var3 + var46 * var45) < 90 && var12[var47] == 1) {
                  h.a(var3, b, 3 + var47, false, false, -1, false);
                  a var52 = b[3 + var47];

                  for(var4 = 0; var4 < var52.k.length; ++var4) {
                     if (var52.k[var4] != null) {
                        var52.k[var4][0] = 0;
                     }
                  }
               }
            }

            GloftMMN.f();
            ++var46;
         }
      case 10:
         short var68;
         for(var3 = 0; var3 < f; ++var3) {
            short var59;
            if ((var59 = e[var3][0]) < 90 && e[var3][5] != -127) {
               var68 = e[var3][1];
               short var66 = e[var3][2];
               a var65 = b[3 + var59];
               c[var3] = new d(var65, var68, var66, (d)null);
               d var63 = c[var3];
               var26 = e[var3][3];
               var69 = e[var3][4];
               int var29;
               if ((var28 = e[var3][10]) > 0) {
                  for(var4 = 0; var4 < 64 && (var29 = var4 * 17) < h.g.length; ++var4) {
                     short var30 = h.g[var29 + 0];
                     short var31;
                     if (h.H == var30 && (var31 = h.g[var29 + 2]) == var28) {
                        int var32;
                        int var33 = ((var32 = h.f[var31 - 1] & 255) & 15) >> 0;
                        int var34 = (var32 & 240) >> 4;
                        var26 = h(var59, var33);
                        var69 = var34;
                        break;
                     }
                  }
               }

               e[var3][3] = (short)var26;
               var63.a(var26);
               if (e[var3][8] == 1) {
                  var63.c = 1;
               }

               var29 = var63.d.h[var63.e] & 255;
               var63.g = 0;
               var63.f = var68 % var29;
               int var71 = var69;
               if (bb[15] == 0 && j()) {
                  var71 = var69 + var65.l / 3;
               } else if (bb[15] == 0 && k()) {
                  var71 = var69 + var65.l / 3 * 2;
               }

               if (var71 < 0) {
                  var71 = 0;
                  e[var3][4] = 0;
                  int var72 = GloftMMN.c(0, var63.d.h.length);
                  e[var3][3] = (short)var72;
                  var63.a(var72);
               } else {
                  e[var3][4] = (short)var71;
               }

               if (var65.w == null || var65.w[var71] == null) {
                  var65.a(var71, 0, -1, -1, var71);
               }
            }
         }

         for(var3 = 3; var3 < b.length; ++var3) {
            if (b[var3] != null) {
               boolean var60 = true;

               for(var4 = 0; var4 < 64 && (var56 = var4 * 17) < h.g.length; ++var4) {
                  var68 = h.g[var56 + 0];
                  if (var1 == var68 && h.g[var56 + 1] == var3 - 3) {
                     var60 = false;
                     break;
                  }
               }

               if (!var8 || var60) {
                  b[var3].b();
               }
            }
         }

         c();
         break;
      case 11:
         byte[] var14 = j[y];
         Object var15 = null;
         k = new short[t * u];
         l = new byte[512];

         for(var3 = 0; var3 < u; ++var3) {
            for(var4 = 0; var4 < t; ++var4) {
               short var16 = 0;
               var17 = var14[2 + var4 + var3 * t] & 15;
               byte var19 = 0;

               for(var18 = 0; var18 < h.ar.length / 3 && (h.ar[var18 * 3 + 0] != var1 || h.ar[var18 * 3 + 1] + 1 != var17 || (var19 = h.ar[var18 * 3 + 2]) != 1 && var19 != 5 && var19 != 4); ++var18) {
               }

               if (var18 != h.ar.length / 3) {
                  if (var19 == 1) {
                     var16 = 3;
                  } else if (var19 == 5) {
                     var16 = 16384;
                  } else {
                     var16 = -32768;
                  }
               } else if (var17 == 0 || (h[B + (var17 - 1) * 3 + 2] & 255) != 0) {
                  var16 = 1;
               }

               k[var4 + var3 * t] = var16;
            }
         }

         int var50 = 0;

         for(var3 = 0; var3 < l.length; ++var3) {
            l[var3] = -1;
         }

         for(var3 = 0; var3 < f; ++var3) {
            short var48;
            boolean var51;
            if ((var51 = (var48 = e[var3][0]) >= 90) || e[var3][5] != -127) {
               var54 = -1;
               var20 = -1;
               if (!var51) {
                  short var58 = e[var3][3];
                  var54 = b(var48, 1, var58);
                  var20 = b(var48, 2, var58);
               }

               if (var54 > 4 || var51) {
                  var54 = 1;
                  var20 = 1;
               }

               var21 = !var51 && e[var3][8] == 1;
               int[] var67 = new int[]{0, 0};
               short var5;
               short var6;
               if (var51) {
                  var5 = -1;
                  var6 = -1;
               } else {
                  var5 = e[var3][5];
                  var6 = e[var3][6];
               }

               if (!var51 && var5 != -1 && var6 != -1) {
                  var67[0] = e[var3][6] + 1;
                  var67[1] = e[var3][5] + 1;
                  if (!var51) {
                     e[var3][9] = -1;
                  }
               } else {
                  if (!var51) {
                     e[var3][9] = -2;
                  }

                  var5 = e[var3][1];
                  var38 = e[var3][2];
                  if (var51) {
                     var37 = var5 + 22;
                     var38 -= 11;
                  } else if (var21) {
                     var37 = var5 - 11;
                  } else {
                     var37 = var5 + 11;
                  }

                  b(var37, var38, var67);
                  var67[0] >>= 8;
                  var67[1] >>= 8;
               }

               if (!var51) {
                  e[var3][5] = (short)var67[0];
                  e[var3][6] = (short)var67[1];
               }

               if (var67[0] < 0) {
                  var67[0] = 0;
               }

               if (var67[1] < 0) {
                  var67[1] = 0;
               }

               if (var21) {
                  var61 = var54;
                  var54 = var20;
                  var20 = var61;
               }

               for(var4 = 0; var4 < var20; ++var4) {
                  for(var61 = 0; var61 < var54; ++var61) {
                     var37 = var67[0] + var4;
                     var38 = var67[1] - var61;
                     if (var37 >= 0 && var37 < t && var38 >= 0 && var38 < u) {
                        if (((var27 = k[var37 + var38 * t]) & 3) == 2) {
                           var26 = (var27 & 508) >> 2;

                           for(var24 = 0; var24 < 4; ++var24) {
                           }
                        } else {
                           var26 = var50++;
                        }

                        for(var24 = 0; var24 < 4; ++var24) {
                           if (l[var26 * 4 + var24] == -1) {
                              l[var26 * 4 + var24] = (byte)(var3 & 255);
                              break;
                           }
                        }

                        if ((var28 = k[var37 + var38 * t]) != 1 && var28 != 16384 && var28 != -32768) {
                           k[var37 + var38 * t] = (short)(2 | var26 << 2);
                        }
                     }
                  }
               }
            }
         }

         for(var3 = 0; var3 < k.length; ++var3) {
            if (k[var3] == 3) {
               k[var3] = 1;
            }
         }

         h.c();
         break;
      case 12:
         if (!var8) {
            if (!ah && h.X == 2) {
               int[] var55 = new int[]{37, 44, 45, 49, 50, 51};
               byte var2 = 0;
               if (var7.a[0] == 0) {
                  var2 = 3;
               }

               for(var3 = 0; var3 < 3; ++var3) {
                  h.s[1 + var3] = new g(-1, 1 + var3, var55[var3 + var2], false, false);
                  h.s[1 + var3].b[6] = (byte)(1 + var3);
                  h.s[1 + var3].a();
               }
            }

            g.a(false);
            if (ah || h.X == 2) {
               for(var3 = 0; var3 < 3; ++var3) {
                  a(h.s[1 + var3], var3);
               }

               for(var3 = 0; var3 < 3; ++var3) {
                  b(1 + var3);
               }

               if (ah) {
                  a((g)h.s[4], 3);
                  b(4);
               }

               g.a(true);
               g.a(false);
            }

            s();
            if (g.v > 6) {
               g.v = 0;
            }
         }
         break;
      case 13:
         GloftMMN.b("/11");
         h.a(0, b, 0, false, false, 256, false);
         if (bb[15] == 0) {
            if (j()) {
               a(2, (a)b[0]);
            } else if (k()) {
               a(3, (a)b[0]);
            } else {
               a(1, (a)b[0]);
            }
         } else {
            a(1, (a)b[0]);
         }

         GloftMMN.f();
         bf = new byte[16];
         var20 = r * s;
         var3 = 0;

         while(true) {
            if (var3 >= var20) {
               break label1363;
            }

            if ((var56 = p[2 + var3] & 15) > 0) {
               --var56;
               bf[var56] = 1;
            }

            ++var3;
         }
      case 14:
         var21 = false;
         bh = -1;
         if (var1 == 0 || var1 == 1 || var1 == 22 || var1 == 34) {
            var21 = true;
            if (var1 == 0) {
               bh = 0;
            }

            if (var1 == 1) {
               bh = 1;
            }

            if (var1 == 22) {
               bh = 2;
            }

            if (var1 == 34) {
               bh = 3;
            }
         }

         for(var3 = 0; var3 < 15; ++var3) {
            if (bf[var3] != 0) {
               var57 = h[B + var3 * 3 + 1] & 255;
               var61 = h[B + var3 * 3 + 0] & 255;
               if (var21) {
                  if (var3 == 0) {
                     var61 = h.bM[bh * 4 + 2] & 255;
                     var57 = h.bM[bh * 4 + 3] & 255;
                  }

                  if (var3 == 1) {
                     var61 = h.bM[bh * 4 + 0] & 255;
                     var57 = h.bM[bh * 4 + 1] & 255;
                  } else if (var3 == 2) {
                     var61 = (h.bM[bh * 4 + 0] & 255) + 1;
                     var57 = h.bM[bh * 4 + 1] & 255;
                  }

                  h[B + var3 * 3 + 0] = (byte)var61;
                  h[B + var3 * 3 + 1] = (byte)var57;
               }

               if (var57 == 255) {
                  var57 = 0;
               }

               if (var61 != 255) {
                  b[0].a(var57, var61, var61, -1, var57);
               }
            }
         }

         bf = null;
         if (h.y != 52) {
            b[0].b();
            b[0].c();
         }
         break;
      case 15:
         GloftMMN.b("/12");
         h.a(0, b, 1, false, false, 64, false);
         if (bb[15] != 0 && bb[15] != 2) {
            a(1, (a)b[1]);
         } else if (j()) {
            a(2, (a)b[1]);
         } else if (k()) {
            a(3, (a)b[1]);
         } else {
            a(1, (a)b[1]);
         }

         GloftMMN.f();
         bf = new byte[16];
         var20 = r * s;
         var3 = 0;

         while(true) {
            if (var3 >= var20) {
               break label1363;
            }

            if ((var57 = q[2 + var3] & 15) > 0) {
               --var57;
               bf[var57] = 1;
            }

            ++var3;
         }
      case 16:
         for(var3 = 0; var3 < 15; ++var3) {
            if (bf[var3] != 0) {
               var57 = h[B + 45 + var3 * 3 + 1] & 255;
               var61 = h[B + 45 + var3 * 3 + 0] & 255;
               if (var57 == 255) {
                  var57 = 0;
               }

               if (var61 != 255) {
                  b[1].a(var57, var61, var61, -1, var57);
               }
            }
         }

         b[1].b();
         b[1].c();
         break;
      case 17:
         a.v = null;
         GloftMMN.a("STARTING TO REALLOCATE");
         byte var36;
         if ((var36 = g.am[h.H]) >= 0) {
            GloftMMN.b("/19");
            GloftMMN.a(var36);
            GloftMMN.f();
            System.gc();
            a = var36 + 1;
         } else {
            a = -1;
         }

         GloftMMN.a("LEVEL MUSIC LOADED");
         break;
      case 18:
         GloftMMN.a("/3", 0);
         GloftMMN.a("TXTS LOADED");
         break;
      case 19:
         if (!var8) {
            b(true);
         }

         GloftMMN.a("IFACE LOADED");
         break;
      case 20:
         a();
         GloftMMN.a("FONT LOADED");
         break;
      case 21:
         if (m == null) {
            m = Image.createImage(n, o);
            aj = m.getGraphics();
         }

         GloftMMN.a("BB LOADED");
         break;
      case 22:
         GloftMMN.a("BEFORE MISC ARRAYS " + GloftMMN.g);
         Y = new byte[15];
         Z = new byte[15];
         aa = new byte[15];
         ab = new byte[15];
         ac = new byte[15];
         ad = new byte[15];

         for(var3 = 0; var3 < 15; ++var3) {
            Y[var3] = (byte)(h[B + var3 * 3 + 0] & 255);
            Z[var3] = (byte)(h[B + var3 * 3 + 1] & 255);
            aa[var3] = (byte)(h[B + 45 + var3 * 3 + 0] & 255);
            ab[var3] = (byte)(h[B + 45 + var3 * 3 + 1] & 255);
            ac[var3] = (byte)(h[B + 45 + var3 * 3 + 2] * 136 / 100);
            ad[var3] = (byte)(h[B + var3 * 3 + 2] & 255);
         }

         ae = new byte[f];
         af = new byte[f];
         ag = new int[f][];

         for(var3 = 0; var3 < f; ++var3) {
            var10 = e[var3];
            d var22 = c[var3];
            if (var10[0] < 90 && var10[5] != -127) {
               ae[var3] = (byte)b(var10[0], 1, var10[3]);
               af[var3] = (byte)b(var10[0], 2, var10[3]);
               var22.a = var10[1] << 8;
               var22.b = var10[2] << 8;
               int[] var23 = var22.c();
               ag[var3] = new int[4];
               ag[var3][0] = var23[0] >> 8;
               ag[var3][1] = var23[1] >> 8;
               ag[var3][2] = var23[2] >> 8;
               ag[var3][3] = var23[3] >> 8;
            }
         }

         g.L = -1;
         aR = -1;
         au = -1;
         d = true;
         av = -1;
         au = -1;
         bg = (byte)h.H;
         C = -1000;
         D = -1000;
         if (!var8) {
            h.y = 19;
         }

         h.z = -1;
         h.i = -1;
         h.L = 0;
         h.K = 0;
         var0.setClip(0, 0, 240, 400);
         var0.setColor(0);
         var0.fillRect(0, 0, 240, 400);
         if (ai >= 0) {
            g.a(ai);
            ai = -1;
         }

         if (h.X == 1) {
            h.N[0] = 20;
            h.N[1] = 0;
            h.N[2] = 0;
            h.N[3] = 1;
            h.N[5] = 35;
            d = true;
            h.y = 0;
         } else if (h.X == 2) {
            h.s[0].a[22] = 0;
            bc[30] = 0;
            h.N[0] = 5;
            h.N[1] = 0;
            h.N[2] = 0;
            h.N[3] = 1;
            h.s[4].a();
            a((g)h.s[4], 3);

            for(var3 = 0; var3 < h.e.length; ++var3) {
               h.e[var3] = -1;
            }

            g.a((int)h.s[0].a[6], (int)-1, (byte)((byte)h.s[0].a[7]));
            g.a((int)-1, (int)h.s[0].a[8], (byte)((byte)h.s[0].a[9]));
         }

         if (!ah && (g.ae & 255) == 0) {
            g.ae &= -257;
         }

         ah = false;
         if (h.X != 1) {
            h.X = 0;
         }

         g.c(h.H);
         GloftMMN.f();
         g.au = false;
         if (h.n[0] != null) {
            h.n[0].a(0);
         }

         h.O = false;
         var57 = bi;
         var61 = bj;
         bi = 0;
         bj = 0;

         for(var3 = 0; var3 < 64; ++var3) {
            if (h.f[var3] != -1) {
               var24 = -1;

               for(var4 = 0; var4 < h.g.length; var4 += 17) {
                  if (h.g[var4 + 2] - 1 == var3) {
                     var24 = var4 / 17;
                     break;
                  }
               }

               short var25 = h.g[var24 * 17 + 0];
               var26 = (h.f[var3] & 15) >> 0;
               var27 = h.g[var24 * 17 + 3 + var26];
               if (var25 != 22 && var25 != 34) {
                  bi += var27;
               } else {
                  bj += var27;
               }
            }
         }

         if (var57 != 0 && (var57 != bi || var61 != bj)) {
            g.i();
         }

         if (a != -1 && GloftMMN.f) {
            GloftMMN.a("BEFORE MUSIC PLAY");
            GloftMMN.c(5);
         }

         return true;
      }

      if (!h.Z) {
         if (ah) {
            var0.setClip(0, 0, 231 * (bd + 8) / 29, 400);
         } else {
            var0.setClip(0, 0, 231 * bd / 21, 400);
         }

         var0.setColor(16711680);
         var0.fillRect(7, 316, 240, 3);
      } else {
         var0.setColor(0);
         var0.fillRect(0, 0, 240, 400);
      }

      ++bd;
      return false;
   }

   private static void a(g var0, int var1) {
      var0.a(7 + var0.a[0] * 2, var0.b[6], true);
      d var2;
      (var2 = h.n[var0.b[5]]).a(66);
      var2.d.m = var0.b[15];
      int[] var3;
      int var4 = (var3 = var2.c())[2] - var3[0] >> 8;
      int var5 = var3[3] - var3[1] >> 8;
      g.U[var1] = Image.createImage(var4, var5);
      Graphics var6;
      (var6 = g.U[var1].getGraphics()).setColor(16777215);
      var6.fillRect(0, 0, var4, var5);
      var2.a = var4 / 2 << 8;
      var2.b = var5 / 2 << 8;
      var2.a(var6);
   }

   static void c() {
      for(int var0 = 0; var0 < c.length; ++var0) {
         short var1;
         if (c[var0] != null && e[var0][0] == 44 && ((var1 = e[var0][3]) == 0 || var1 == 1)) {
            if (bb[0] == 0) {
               c[var0].a(0);
            } else {
               c[var0].a(1);
            }

            e[var0][3] = (short)c[var0].e;
         }
      }

   }

   static int a(int var0) {
      int var1 = var0 >> 10 & 31;
      int var2 = var0 >> 5 & 31;
      int var3 = var0 >> 0 & 31;
      int var4 = (var0 >> 15 & 1) * 255;
      var1 = var1 * 255 / 31;
      var2 = var2 * 255 / 31;
      var3 = var3 * 255 / 31;
      return var4 << 24 | var1 << 16 | var2 << 8 | var3;
   }

   private static int a(d var0) {
      int var1 = (var0.d.i[var0.e] + var0.f) * 5;
      return var0.d.j[var1] & 255;
   }

   static int a(int var0, int var1) {
      for(int var2 = 0; var2 < f; ++var2) {
         if (e[var2][0] == var0) {
            if (var1 == -1) {
               return var2;
            }

            if (e[var2][3] == var1) {
               return var2;
            }
         }
      }

      return -1;
   }

   static void a(int var0, int[] var1) {
      int var2 = e[var0][1] + 22;
      int var3 = e[var0][2] - 11;
      b(var2, var3, var1);
      var1[0] &= -256;
      var1[1] &= -256;
      var1[0] += 127;
      var1[1] += 127;
   }

   private static void j(int var0, int var1) {
      ak[0] = var1;
      ak[1] = var0;
      byte var2 = bb[14];
      int var3 = 0 + (var0 == 0 ? 10 : 2);
      if (var2 == 1) {
         var3 += 0;
      } else if (var2 == 2) {
         var3 += 2;
      } else if (var2 == 3) {
         var3 += 4;
      } else if (var2 == 4) {
         var3 += 6;
      } else if (l()) {
         var3 += 0;
      } else if (j()) {
         var3 += 6;
      } else if (k()) {
         var3 += 4;
      } else {
         var3 += 2;
      }

      int var4;
      for(var4 = 0; var4 < b[2].k.length; ++var4) {
         if (b[2].k[var4] != null) {
            b[2].k[var4][1] = ak[var3];
         }
      }

      b[2].a(0, 0, -1, -1, 0);
      var4 = 1 + (ak[1] == 0 ? 10 : 2);
      if (var2 == 1) {
         var4 += 0;
      } else if (var2 == 2) {
         var4 += 2;
      } else if (var2 == 3) {
         var4 += 4;
      } else if (var2 == 4) {
         var4 += 6;
      } else if (l()) {
         var4 += 0;
      } else if (j()) {
         var4 += 6;
      } else if (k()) {
         var4 += 4;
      } else {
         var4 += 2;
      }

      bl = ak[var4];
   }

   static void a(int var0, int var1, int[] var2) {
      c(var0, var1, var2);
      var2[0] -= C;
      var2[1] -= D;
   }

   private static void c(int var0, int var1, int[] var2) {
      int var3 = s * 44 / 2;
      int var5 = (var0 * w[0] + var1 * x[0] >> 8) * 44;
      int var6 = (var0 * w[1] + var1 * x[1] >> 8) * 22;
      var5 >>= 8;
      var6 >>= 8;
      var5 += var3;
      var6 += 54;
      var2[0] = var5;
      var2[1] = var6;
   }

   static void b(int var0, int var1, int[] var2) {
      int var3 = s * 44 / 2 << 8;
      int var5 = var0 << 8;
      int var6 = var1 << 8;
      var2[0] = (var5 - var3) / 44 + (var6 - 13824) / 22;
      var2[1] = (var6 - 13824) / 22 - (var5 - var3) / 44;
   }

   private static int c(int var0, int var1, int var2) {
      return var0 == 0 ? p[2 + var1 + var2 * r] & 255 & 15 : q[2 + var1 + var2 * r] & 255;
   }

   private static int k(int var0, int var1) {
      int var2 = 0;
      int[][] var3 = new int[][]{{-1, 0}, {1, 0}, {0, -1}, {0, 1}};

      for(int var4 = 0; var4 < 4; ++var4) {
         int var5 = var0 + var3[var4][0];
         int var6 = var1 + var3[var4][1];
         if (var5 >= 0 && var5 < r && var6 >= 0 && var6 < s && c(0, var5, var6) != 0) {
            ++var2;
         }
      }

      return var2;
   }

   static boolean a(int var0, int var1, int var2) {
      int var3 = b(var0 - var2 / 2, var1 - var2 / 2);
      int var4 = b(var0 + var2 / 2, var1 - var2 / 2);
      int var5 = b(var0 + var2 / 2, var1 + var2 / 2);
      int var6 = b(var0 - var2 / 2, var1 + var2 / 2);
      return var3 == 1 || a(var3, var0, var1, 0, true) || var4 == 1 || a(var4, var0, var1, 0, true) || var5 == 1 || a(var5, var0, var1, 0, true) || var6 == 1 || a(var6, var0, var1, 0, true);
   }

   static int b(int var0, int var1) {
      if (var0 >= 0 && var0 < t << 8 && var1 >= 0 && var1 < u << 8) {
         var0 >>= 8;
         var1 >>= 8;
         return k[var0 + var1 * t];
      } else {
         return 1;
      }
   }

   static boolean a(int var0, int var1, int var2, int var3, boolean var4) {
      int var5;
      if ((var0 & 15872) != 0) {
         if (var1 == -1 || var2 == -1) {
            return true;
         }

         var5 = (var0 & 15872) >> 9;
         if (var3 != var5) {
            if (!var4) {
               return true;
            }

            g var6;
            var1 = (var6 = h.s[var5]).c[0] - var1;
            var2 = var6.c[1] - var2;
            if (GloftMMN.a((long)(var1 * var1 + var2 * var2)) <= 128) {
               return true;
            }
         }
      }

      if ((var0 & 3) == 2) {
         var5 = (var0 & 508) >> 2;

         for(int var9 = 0; var9 < 4; ++var9) {
            byte var7;
            if ((var7 = l[var5 * 4 + var9]) != -1) {
               int var10 = var7 & 255;
               if (e[var10][0] < 90 && b(e[var10][0], 1, e[var10][3]) < 5) {
                  return true;
               }
            }
         }
      }

      return false;
   }

   private static void a(Graphics var0, Image var1, int var2, int var3, int var4) {
      if (b(var2, var3, var1.getWidth(), var1.getHeight(), S, T, S + U, T + V)) {
         var0.drawImage(var1, var2, var3, var4);
      }

   }

   private static boolean b(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7) {
      if (var0 >= var6) {
         return false;
      } else if (var0 + var2 < var4) {
         return false;
      } else if (var1 >= var7) {
         return false;
      } else {
         return var1 + var3 >= var5;
      }
   }

   static boolean a(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7) {
      if (var0 >= var6) {
         return false;
      } else if (var2 < var4) {
         return false;
      } else if (var1 >= var7) {
         return false;
      } else {
         return var3 >= var5;
      }
   }

   private static void a(Graphics var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7) {
      g.getClipWidth();
      g.getClipHeight();
      g.setClip(var6, var7, var4, var5);
      S = var6;
      T = var7;
      U = var4;
      V = var5;
      if (var1 < 3) {
         var5 += 87;
      }

      int var10 = var4;
      int var11 = var5;
      if (var4 < 44) {
         var10 = 44;
      }

      if (var5 < 22) {
         var11 = 22;
      }

      if (var1 == 0) {
         g.setColor(bl);
         g.fillRect(var6, var7, var4, var5);
      }

      boolean var21 = false;
      b(var2, var3, M);
      b(var2 + var10, var3, N);
      b(var2 + var10, var3 + var11, O);
      b(var2, var3 + var11, P);
      M[0] >>= 8;
      M[1] >>= 8;
      N[0] >>= 8;
      N[1] >>= 8;
      O[0] >>= 8;
      O[1] >>= 8;
      P[0] >>= 8;
      P[1] >>= 8;
      int var56 = P[1] - N[1] + 1;
      int var22 = (M[0] - N[0] << 8) / (M[1] - N[1]);
      int var23 = (O[0] - N[0] << 8) / (O[1] - N[1]);
      int var24 = (P[0] - M[0] << 8) / (P[1] - M[1]);
      int var25 = (P[0] - O[0] << 8) / (P[1] - O[1]);

      int var9;
      int var26;
      for(var26 = 0; var26 < var56; ++var26) {
         if ((var9 = var26 + N[1]) < O[1]) {
            R[var26] = (N[0] << 8) + (var9 - N[1]) * var23;
         } else {
            R[var26] = (O[0] << 8) + (var9 - O[1]) * var25;
         }

         if (var9 < M[1]) {
            Q[var26] = (N[0] << 8) + (var9 - N[1]) * var22;
         } else {
            Q[var26] = (M[0] << 8) + (var9 - M[1]) * var24;
         }
      }

      d var27 = null;
      Object var28 = null;
      int[] var29;
      int var31;
      int var32;
      short[] var57;
      int var58;
      if (var1 == 2) {
         var32 = var6 - var2;
         var58 = var7 - var3;

         for(var26 = 0; var26 < X; ++var26) {
            var31 = W[var26] & 255;
            if (a((var29 = ag[var31])[0] + var32, var29[1] + var58, var29[2] + var32, var29[3] + var58, S, T, S + U, T + V)) {
               var27 = c[var31];
               var57 = e[var31];
               var27.a = var32 + var57[1] << 8;
               var27.b = var58 + var57[2] << 8;
               var27.d.m = var57[4];
               var27.a(g);
            }
         }

      } else {
         byte[] var30 = var1 == 0 ? p : q;
         var32 = s * 44 / 2;
         boolean var33 = false;
         var32 += var6 - var2 - 22;
         var58 = 54 + var7 - var3 + 22;
         Image[][] var34 = (Image[][])null;
         if (var1 == 0) {
            var34 = b[0].w;
         } else if (var1 == 1) {
            var34 = b[1].w;
         }

         int var39 = -1;
         int var40 = -1;
         if (var1 == 3) {
            g var41 = null;
            if (h.s != null) {
               var39 = (var41 = h.s[0]).c[0] >> 8;
               var40 = var41.c[1] >> 8;
            }
         }

         for(var26 = 0; var26 < var56; ++var26) {
            if ((var9 = N[1] + var26) >= 0 && var9 < s) {
               int var12;
               int var13;
               if (var1 == 3) {
                  var12 = (Q[var26] >> 8) - 2;
                  var13 = (R[var26] >> 8) + 2;
               } else {
                  var12 = (Q[var26] >> 8) - 1;
                  var13 = (R[var26] >> 8) + 1;
               }

               var31 = 2 + var9 * r;
               int var47 = var9 * t;

               for(int var8 = var12; var8 <= var13; ++var8) {
                  if (var8 >= 0 && var8 < r) {
                     if (var1 != 3) {
                        int var16;
                        if (var1 <= 2 && (var16 = var30[var31 + var8] & 15) > 0) {
                           int var14 = var32 + (var8 - var9) * 44 / 2;
                           int var15 = var58 + (var8 + var9) * 22 / 2;
                           int var17;
                           if (var1 == 0) {
                              var17 = var16;
                           } else {
                              var17 = p[var31 + var8] & 15;
                           }

                           if (var17 > 0 && ad[var17 - 1] == 1) {
                              var14 += 22;
                           }

                           --var16;
                           Image var35;
                           int var36;
                           if (var1 == 0) {
                              byte var54;
                              if ((var54 = (byte)((var30[var31 + var8] & 240) >> 4)) >= 1) {
                                 --var54;
                                 b[2].a((Graphics)g, var54, var14 + L[var54][0], var15 + L[var54][1], 0, 0, 0);
                              }

                              var36 = Y[var16] & 255;
                              var35 = var34[Z[var16] & 255][var36];
                              a(g, var35, var14, var15 - var35.getHeight(), 20);
                              if ((h[B + var16 * 3 + 2] & 255) == 2 && var8 < r - 1 && (var16 = var30[var31 + var8 + 1] & 15) > 0 && (h[B + (var16 - 1) * 3 + 2] & 255) == 1) {
                                 a(g, var35, var14 + 22, var15 - var35.getHeight() + 11, 20);
                              }
                           } else if (var1 == 1 && (var36 = aa[var16] & 255) != 255) {
                              var35 = var34[ab[var16] & 255][var36];
                              a(g, var35, var14, var15 - var35.getHeight() - ac[var16], 20);
                           }
                        }
                     } else {
                        short var59;
                        if (((var59 = k[var8 + var47]) & 3) == 2) {
                           int var43 = (var59 & 508) >> 2 << 2;

                           byte var44;
                           for(int var45 = 0; var45 < 4 && (var44 = l[var43 + var45]) != -1; ++var45) {
                              int var60 = var44 & 255;
                              if ((var57 = e[var60])[0] < 90) {
                                 if (((var27 = c[var60]).d.h[var27.e] & 255) > 1) {
                                    var0.setClip(0, 75, 240, 240);
                                    var27.a = var57[1] - C + 0 << 8;
                                    var27.b = var57[2] - D + 75 << 8;
                                    var27.d.m = var57[4];
                                    var27.a(var0);
                                    int[] var10000 = var29 = var27.c();
                                    var10000[0] >>= 8;
                                    var29[1] >>= 8;
                                    var29[2] >>= 8;
                                    var29[3] >>= 8;
                                    a(var0, var29, var8, var9, (g)null);
                                    var27.b();
                                 }

                                 d(var0, var60);
                              }
                           }
                        }

                        int var42 = (var59 & 15872) >> 9;
                        boolean var46 = var8 == var39 && var9 == var40;
                        int var49 = 0;
                        byte var50 = 1;
                        g var53;
                        int var51 = (var53 = h.s[0]).c[0] + var53.c[1];
                        int var52 = 0;
                        if (var42 > 0) {
                           var52 = (var53 = h.s[var42]).c[0] + var53.c[1];
                        }

                        if (var52 > var51) {
                           var50 = 0;
                        }

                        var0.setClip(0, 75, 240, 240);

                        for(; var49 < 2; ++var49) {
                           if (var49 != var50 && var42 > 0) {
                              var0.setClip(0, 75, 240, 240);
                              var53.a(var0, false);
                           }

                           if (var49 == var50 && var46) {
                              var0.setClip(0, 75, 240, 240);
                              g = var0;
                              c(false);
                           }
                        }

                        if (var42 > 0) {
                           var53.a(var0, true);
                        }

                        if (var46) {
                           c(true);
                        }
                     }
                  }
               }
            }
         }

      }
   }

   static void a(Graphics var0) {
      int[] var10000 = new int[]{0, 0};
      g = aj;
      int var3 = m.getWidth();
      int var4 = m.getHeight();
      int var5;
      if ((var5 = C % var3) < 0) {
         var5 += var3;
      }

      int var6;
      if ((var6 = D % var4) < 0) {
         var6 += var4;
      }

      int var7 = var3 - var5;
      int var8 = var4 - var6;
      d var9 = null;
      Object var10 = null;
      int var12 = C - E;
      int var13 = D - F;
      X = 0;

      int var1;
      for(var1 = 0; var1 < f; ++var1) {
         var9 = c[var1];
         short[] var28;
         if ((var28 = e[var1])[0] < 90 && var28[5] != -127 && ae[var1] <= 7) {
            var9.a = var28[1] - C << 8;
            var9.b = var28[2] - D << 8;
            int[] var11;
            if (a((var11 = ag[var1])[0] - C, var11[1] - D, var11[2] - C, var11[3] - D, 0, 0, var3, var4)) {
               if ((var9.d.h[var9.e] & 255) == 1) {
                  W[X] = (byte)var1;
                  ++X;
               }

               var9.d.m = var28[4];
            }
         }
      }

      int var15;
      if (var12 <= 44 && var13 <= 44) {
         byte var20;
         if (var12 > 0 || var12 < 0) {
            int var16;
            int var18;
            if (var12 > 0) {
               var16 = var5 - 22;
               var18 = C + var3 - 22;
            } else {
               var16 = var5;
               var18 = C;
            }

            var20 = 1;
            if (var16 < 0 || var16 + 22 >= var3) {
               var20 = 2;
            }

            var15 = 0;

            while(true) {
               if (var15 >= 3) {
                  E = C;
                  break;
               }

               for(var1 = 0; var1 < var20; ++var1) {
                  if (var1 == 1) {
                     if (var16 < 0) {
                        var16 += var3;
                     } else if (var16 + 22 >= var3) {
                        var16 = -(var3 - var16);
                     }
                  }

                  a((Graphics)null, var15, var18, D + var8, 22, var6, var16, 0);
                  a((Graphics)null, var15, var18, D, 22, var8, var16, var6);
               }

               ++var15;
            }
         }

         if (var13 > 0 || var13 < 0) {
            int var17;
            int var19;
            if (var13 > 0) {
               var19 = D + var4 - 22;
               var17 = var6 - 22;
            } else {
               var19 = D;
               var17 = var6;
            }

            var20 = 1;
            if (var17 < 0 || var17 + 22 >= var4) {
               var20 = 2;
            }

            for(var15 = 0; var15 < 3; ++var15) {
               for(var1 = 0; var1 < var20; ++var1) {
                  if (var1 == 1) {
                     if (var17 < 0) {
                        var17 += var4;
                     } else if (var17 + 22 >= var4) {
                        var17 = -(var4 - var17);
                     }
                  }

                  try {
                     a((Graphics)null, var15, C, var19, var7, 22, var5, var17);
                  } catch (Exception var27) {
                     var27.toString();
                  }

                  try {
                     a((Graphics)null, var15, C + var7, var19, var5, 22, 0, var17);
                  } catch (Exception var26) {
                     var26.toString();
                  }
               }
            }

            F = D;
         }
      } else {
         for(var15 = 0; var15 < 3; ++var15) {
            a((Graphics)null, var15, C, D, var7, var8, var5, var6);
            a((Graphics)null, var15, C + var7, D + var8, var5, var6, 0, 0);
            a((Graphics)null, var15, C, D + var8, var7, var6, var5, 0);
            a((Graphics)null, var15, C + var7, D, var5, var8, 0, var6);
         }

         E = C;
         F = D;
      }

      var0.setClip(0, 75, 240, 240);

      try {
         a(var0, 0, 0, var5, var6, var7, var8);
      } catch (Exception var25) {
      }

      try {
         a(var0, 0, var6, var5, var8, var7, 0);
      } catch (Exception var24) {
      }

      try {
         a(var0, var5, 0, var7, var6, 0, var8);
      } catch (Exception var23) {
      }

      try {
         a(var0, var5, var6, var7, var8, 0, 0);
      } catch (Exception var22) {
      }

      var0.setClip(0, 75, 240, 240);
      a(var0, 3, C, D, 240, 240, 0, 75);
      d(var0);
   }

   private static void a(Graphics var0, int var1, int var2, int var3, int var4, int var5, int var6) {
      if (var5 + var3 >= 240) {
         var3 -= var5 + var3 - 240;
      }

      if (var6 + var4 >= 240) {
         var4 -= var6 + var4 - 240;
      }

      if (var1 < 0) {
         System.out.println("xxxxxxxxxxxxxxx lesssssssss");
         var1 = 0;
      }

      if (var2 < 0) {
         System.out.println("yyyyyyyyyyyyyyyy lesssssssss");
         var2 = 0;
      }

      if (var3 > 0 && var4 > 0 && var1 >= 0 && var2 >= 0) {
         var0.drawRegion(m, var1, var2, var3, var4, 0, var5 + 0, var6 + 75, 20);
      }

   }

   private static void a(Graphics var0, int[] var1, int var2, int var3, g var4) {
      d var13 = null;
      Object var14 = null;
      int var15 = var2 + var3;
      int var16 = -C + 0;
      int var17 = -D + 75;

      for(int var6 = 0; var6 < X; ++var6) {
         int var7 = W[var6] & 255;
         if (ae[var7] != 6) {
            int[] var8 = ag[var7];
            short[] var21;
            int var9 = (var21 = e[var7])[5] & 255;
            int var10 = var21[6] & 255;
            byte var11 = ae[var7];
            byte var12 = af[var7];
            int var5;
            if (ae[var7] < 5 && var21[8] == 1) {
               if (var21[9] == -1) {
                  var5 = var9 + var11 - 1 + (var10 - (var12 - 1));
               } else {
                  var5 = var9 - (var11 - 1) + var10 + (var12 - 1);
               }
            } else {
               var5 = var9 + var10;
            }

            if ((ae[var7] == 7 || var5 > var15 && (var9 >= var2 || var10 >= var3) || var5 == var15 && var11 < var12) && a(var8[0] + var16, var8[1] + var17, var8[2] + var16, var8[3] + var17, var1[0], var1[1], var1[2], var1[3])) {
               var11 = ae[var7];
               var12 = af[var7];
               boolean var18 = false;
               if (var4 != null && (var11 > 1 || var12 > 1)) {
                  int var19 = var4.c[0] >> 8;
                  int var20 = var4.c[1] >> 8;
                  if (var21[8] == 1) {
                     if (var19 >= var9 && var19 < var9 + var11 && var20 > var10 - var12 && var20 <= var10) {
                        var18 = true;
                     }
                  } else if (var19 >= var9 && var19 < var9 + var12 && var20 > var10 - var11 && var20 <= var10) {
                     var18 = true;
                  }
               }

               if (!var18) {
                  var0.setClip(var1[0], var1[1], var1[2] - var1[0], var1[3] - var1[1]);
                  var0.clipRect(0, 75, 240, 240);
                  (var13 = c[var7]).a = var16 + var21[1] << 8;
                  var13.b = var17 + var21[2] << 8;
                  var13.d.m = var21[4];
                  var13.a(var0);
               }
            }
         }
      }

   }

   static void a(Graphics var0, g var1) {
      a(var0, var1.d, var1.c[0] >> 8, var1.c[1] >> 8, var1);
   }

   static int a(int[] var0) {
      int var1 = var0[0] + C;
      int var2 = var0[1] + D;
      Object var4 = null;
      int var5 = -1;
      int var6 = 100000;

      for(int var7 = 0; var7 < f; ++var7) {
         short[] var11;
         if ((var11 = e[var7])[0] == 93 && var1 >= var11[1] && var2 >= var11[2] && var1 < var11[1] + 240 && var2 < var11[2] + 240) {
            int var8 = var1 - (var11[1] + 120);
            int var9 = var2 - (var11[2] + 120);
            int var10;
            if ((var10 = var8 * var8 + var9 * var9) < var6) {
               var5 = var7;
               var6 = var10;
            }
         }
      }

      return var5;
   }

   static int b(int[] var0) {
      if (var0[0] < 40) {
         return 0;
      } else if (var0[0] > 200) {
         return 1;
      } else if (var0[1] - 63 < 40) {
         return 2;
      } else {
         return var0[1] > 200 ? 3 : -1;
      }
   }

   private static void q() {
      G = GloftMMN.j(5);
      H = new short[90];
      int var0 = 0;

      for(int var2 = 0; var2 < 90; ++var2) {
         H[var2] = (short)var0;
         int var1 = G[var0++] & 255;
         var0 += var1;
         var1 = G[var0++] & 255;
         var0 += var1;
      }

   }

   private static String l(int var0, int var1) {
      int var2 = 0;

      for(int var3 = 0; var3 < 90; ++var3) {
         int var4 = b(var3, 3, -1);
         if (var3 == var0) {
            byte var5;
            if ((var5 = I[var2 + var1]) == -1) {
               return "UNKNOWN !";
            }

            return GloftMMN.k(var5 & 255);
         }

         var2 += var4;
      }

      return null;
   }

   static int b(int var0, int var1, int var2) {
      short var3 = H[var0];
      short var10001 = var3;
      int var5 = var3 + 1;
      int var4 = G[var10001] & 255;
      if (var1 == 0) {
         return var4;
      } else if (var1 == 1) {
         return G[var5 + var2] >> 3;
      } else if (var1 == 2) {
         return G[var5 + var2] & 7;
      } else {
         var5 += var4;
         if (var1 == 3) {
            return G[var5] & 255;
         } else {
            return var1 == 4 ? G[var5 + 1 + var2] & 255 : -1;
         }
      }
   }

   private static void c(boolean var0) {
      g var1 = h.s[0];
      d var2 = h.n[0];
      if (h.h != 18) {
         if (!var0) {
            var1.g();
            int[] var3 = new int[]{0, 0};
            if (aD) {
               var2.b();
               return;
            }

            if (h.y != 5 || h.z >= 0) {
               a(var1.c[0], var1.c[1], var3);
               var2.a = var3[0] + 0 << 8;
               var2.b = var3[1] + 75 << 8;
            }

            var2.d.m = 0;
            Image[] var4 = var2.d.w[0];
            int var5 = 0;

            for(int var6 = 0; var6 < var2.d.a; ++var6) {
               if (h.q[var1.a[0]][var6] == 5) {
                  if ((var1.a[0] != 0 || var1.a[4] != 11) && (var1.a[22] <= 40 || bc[30] != 1)) {
                     bc[30] = 0;
                     var4[var6] = g.Z[var5];
                  } else {
                     int var8 = var1.a[0];
                     int var9 = h.p[var8][5].a / h.r[var8][5];
                     int var10 = var1.a[4];
                     if (var1.a[0] == 0) {
                        --var9;
                     }

                     if (var1.a[0] == 0) {
                        int var11 = var9 - 1;
                        if (var10 >= var11) {
                           ++var10;
                        }
                     }

                     if (var10 >= var9) {
                        var10 -= var9;
                        if (var1.a[0] == 0) {
                           var8 = 1;
                           var10 = g.X[var10];
                        } else {
                           var8 = 0;
                           var10 = g.Y[var10];
                        }
                     }

                     a var12 = h.p[var8][5];
                     var4[var6] = var12.w[0][var10 * 3 + var5];
                  }

                  ++var5;
                  if (var5 == 3) {
                     break;
                  }
               }
            }

            var2.a(g);
         }

         var1.d = var2.c();
         int[] var10000 = var1.d;
         var10000[0] >>= 8;
         var10000 = var1.d;
         var10000[1] >>= 8;
         var10000 = var1.d;
         var10000[2] >>= 8;
         var10000 = var1.d;
         var10000[3] >>= 8;
         if (var0) {
            h.aB.setClip(0, 75, 240, 240);
            if (h.y == 0 && (au != -1 || av != -1) && h.X != 1) {
               a(g, (d)null, false, true, false, (g)null);
            }

            d var13 = h.v[19];
            if (g.u >= 0) {
               if (var13.e != g.u) {
                  var13.e = -1;
                  var13.a(g.u);
                  var13.i = var2;
                  var13.a = 0;
                  var13.b = -16128;
               }

               var13.a(g);
               if (var13.e != 76 || !var13.a()) {
                  var13.b();
               }
            } else {
               var13.e = -1;
            }
         }

         if (!var0) {
            a(g, var1);
         }

         if (!var0 && h.y != 1) {
            var2.b();
         }

      }
   }

   private static void d(Graphics var0, int var1) {
      if (h.y == 0 && !ba && var1 == au) {
         d var2 = c[var1];
         short[] var3 = e[var1];
         ba = true;
         var0.setClip(0, 75, 240, 240);
         var2.a = var3[1] - C + 0 << 8;
         var2.b = var3[2] - D + 75 << 8;
         a(var0, var2, true, false, false, (g)null);
      }

   }

   private static void d(Graphics var0) {
      var0.setClip(0, 75, 240, 240);
      if (h.y == 5 && h.z < 0 && ax > 0 && (i(ax, 10) & 255) != 255) {
         d var1;
         (var1 = h.t[0]).d = h.n[0].d;
         var1.d.m = h.s[0].b[15];
         var1.a(var0);
         var1.b();
      }

      for(int var2 = 1; var2 < h.s.length; ++var2) {
         g var3;
         if ((var3 = h.s[var2]) != null && var3.s) {
            d var4 = h.n[var3.b[5]];
            int var5 = (var3.c[0] >> 8) + (var3.c[1] >> 8);
            int var6 = (h.s[0].c[0] >> 8) + (h.s[0].c[1] >> 8);
            if (var4 != null && var4.e == 67) {
               var0.setClip(0, 75, 240, 240);
               var4.d.m = var3.b[15];
               var4.d.a((Graphics)var0, 68, (int)(h.aG / 2L % 15L), var4.a >> 8, var4.b >> 8, var4.c, 0, 0);
               if (var5 < var6) {
                  int[] var7 = new int[4];
                  var4.d.a((int[])var7, 68, (int)(h.aG / 2L % 15L), var4.a, var4.b, var4.c, 0, 0);
                  var7[0] >>= 8;
                  var7[1] >>= 8;
                  var7[2] >>= 8;
                  var7[3] >>= 8;
                  int[] var8 = h.s[0].d;
                  var0.setClip(0, 0, 240, 400);
                  if (a(var7[0], var7[1], var7[2], var7[3], var8[0], var8[1], var8[2], var8[3])) {
                     var0.setClip(var7[0], var7[1], var7[2] - var7[0], var7[3] - var7[1]);
                     h.n[0].d.m = h.s[0].b[15];
                     h.n[0].a(var0);
                     c(true);
                  }
               }
            }
         }
      }

   }

   static int a(int var0, int var1, int var2, int var3) {
      int var4 = var0 - var2 << 8;
      int var5 = var1 - var3 << 8;
      return GloftMMN.a((long)(var4 * var4 + var5 * var5));
   }

   private static int r() {
      for(int var0 = 0; var0 < f; ++var0) {
         if (e[var0][0] < 90 && e[var0][7] == aG) {
            return var0;
         }
      }

      return -1;
   }

   private static void d(int[] var0) {
      var0[0] += C - 0;
      var0[1] += D - 75;
      b(var0[0], var0[1], var0);
   }

   private static int[] g(int var0) {
      short[] var1;
      short var2 = (var1 = e[var0])[0];
      short var3 = var1[3];
      short var4 = var1[5];
      short var5 = var1[6];
      int var7 = b(var2, 1, var3);
      int var8 = b(var2, 2, var3);
      int var9 = 0;

      int var10;
      int var12;
      for(var10 = 1; var10 < h.s.length; ++var10) {
         if (h.s[var10] != null && h.s[var10].b()) {
            int var11 = h.s[var10].c[0] >> 8;
            var12 = h.s[var10].c[1] >> 8;
            if (var11 >= var4 && var11 < var4 + var7 && var12 <= var5 && var12 > var5 - var8) {
               ++var9;
            }
         }
      }

      c[var0].a = var1[1] - C + 0 << 8;
      c[var0].b = var1[2] - D + 75 << 8;
      if (var9 != 0) {
         return c[var0].c(var9);
      } else {
         for(var10 = 0; var10 < 4; ++var10) {
            int[] var13;
            if ((var13 = c[var0].c(var10)) != null) {
               d(var13);
               if (((var12 = b(var13[0], var13[1])) != 1 || h.H == 25) && (var12 & 15872) == 0) {
                  return c[var0].c(var10);
               }
            }
         }

         return null;
      }
   }

   static void a(g var0, int var1, int var2, int var3, int var4) {
      d var5 = h.n[var0.b[5] & 255];
      var0.o[0] = var1;
      var0.o[1] = var2;
      c(var0.o);
      var0.n[0] = var1 * w[0] + var2 * x[0] >> 8;
      var0.n[1] = var1 * w[1] + var2 * x[1] >> 8;
      c(var0.n);
      if (var0.n[1] < 0) {
         var5.a(var3);
      } else {
         var5.a(var4);
      }

      if (var0.n[0] < 0) {
         var5.c = 1;
      } else {
         var5.c = 0;
      }
   }

   private static void h(int var0) {
      g var1 = h.s[0];
      short[] var3;
      int var4 = ((var3 = e[var0])[5] << 8) + 127;
      int var5 = (var3[6] << 8) + 127;
      int var6 = b(var3[0], 1, var3[3]);
      int var7 = b(var3[0], 2, var3[3]);
      if (var6 > 1 && var6 < 5) {
         var4 += (var6 << 8) / 2;
         var5 -= (var7 << 8) / 2;
      }

      int var8 = (var1.c[0] & -256) + 127;
      int var9 = (var1.c[1] & -256) + 127;
      a((g)var1, var4 - var8, var5 - var9, 1, 0);
   }

   static void a(g var0, g var1) {
      int var2 = var1.c[0];
      int var3 = var1.c[1];
      int var4 = var0.c[0];
      int var5 = var0.c[1];
      a((g)var0, var2 - var4, var3 - var5, 1, 0);
   }

   static void c(int[] var0) {
      int var1;
      if ((var1 = GloftMMN.a((long)(var0[0] * var0[0] + var0[1] * var0[1]))) != 0) {
         var0[0] = (var0[0] << 8) / var1;
         var0[1] = (var0[1] << 8) / var1;
      }

   }

   static boolean a(boolean var0, boolean var1) {
      short[] var4 = null;
      if (au >= 0) {
         var4 = e[au];
      }

      g var5;
      if (var0) {
         var5 = h.s[0];
         aG = (byte)i(aX[h.B], 9);
         int[] var9;
         if (aG >= 0) {
            var9 = g(r());
         } else {
            var9 = g(au);
         }

         if (var9 == null) {
            aI[0] = var5.c[0];
            aI[1] = var5.c[1];
         } else {
            d(var9);
            int var2;
            if ((var2 = b(var9[0], var9[1])) != 1 && !a(var2, var9[0], var9[1], 0, true)) {
               aI[0] = var9[0];
               aI[1] = var9[1];
            } else {
               aI[0] = var5.c[0];
               aI[1] = var5.c[1];
            }
         }

         if (var1) {
            var5.c[0] = aI[0];
            var5.c[1] = aI[1];
         } else {
            h.z = 2;
         }

         return false;
      } else {
         boolean var6;
         if ((var6 = (var5 = h.s[0]).a(aI)) && var4 != null) {
            int var7 = (var4[5] << 8) + 127 - var5.c[0];
            int var8 = (var4[6] << 8) + 127 - var5.c[1];
            if (var7 != 0 && var8 != 0) {
               a((g)var5, var7, var8, 1, 0);
            }
         }

         return var6;
      }
   }

   static void a(Graphics var0, boolean var1) {
      g var2 = h.s[av];
      g var3 = h.s[0];
      int var4;
      byte var7;
      int var10;
      int var25;
      if (var1) {
         boolean var22 = false;
         if (var3.c[0] > var2.c[0]) {
            var7 = 1;
         } else {
            var7 = -1;
         }

         int var23 = var2.c[0];
         var25 = var2.c[1];
         var4 = var23 + 160 * var7;
         int var21 = var25 - 160 * var7;
         var10 = b(var4, var21);
         boolean var26 = false;
         var26 = false | (var10 == 1 || a(var10, var4, var21, 0, true));
         int[] var27 = new int[]{0, 0};
         a(var4, var21, var27);
         boolean var31 = var27[0] <= 0 || var27[0] >= 239;
         boolean var30 = var26 | (var27[0] <= 0 || var27[0] >= 239);
         var31 |= var26;
         if (var30) {
            int var24 = var7 * -1;
            var4 = var23 + 160 * var24;
            var21 = var25 - 160 * var24;
            var10 = b(var4, var21);
            var26 = false | (var10 == 1 || a(var10, var4, var21, 0, true));
            a(var4, var21, var27);
            var31 = var27[0] <= 0 || var27[0] >= 239;
            var30 = var26 | (var27[0] <= 0 || var27[0] >= 239);
            var31 |= var26;
            if (var30) {
               g.N.removeAllElements();
               h.z = 0;
               return;
            }
         }

         aI[0] = var4;
         aI[1] = var21;
         var3.c[0] = aI[0];
         var3.c[1] = aI[1];
         b(var0, 4);
         a(var2, var3);
      } else {
         d var5 = h.n[var2.b[5] & 255];
         byte var6 = h.E[h.D - 2];
         var7 = h.E[h.D - 1];
         byte var8;
         byte var9;
         if ((var9 = var8 = h.E[h.D]) == -1) {
            var9 = 0;
         }

         var25 = var9 + var6 * 28 + var7 * 7;
         ++var2.r[var25];
         h.x = (byte)(var10 = var2.r[var25] & 255);
         int var11 = -1;
         var25 = c(var6, var7);
         int var12 = aJ[var25 + 6] & 255;
         int var13 = -1;
         if (var8 != -1) {
            var25 += 7 + var12 * 4;

            for(var4 = 0; var4 <= var8; ++var4) {
               if (var4 == var8) {
                  var13 = aJ[var25 + 0] | aJ[var25 + 0 + 1] << 8;
               }

               var11 = aJ[var25 + 2] & 255;
               var12 = aJ[var25 + 3] & 255;
               var25 += 4;
               if (var4 != var8) {
                  var25 += var12 * 4;
               }
            }
         } else {
            var11 = aJ[var25 + 4] & 255;
            var12 = aJ[var25 + 6] & 255;
            var25 += 7;
         }

         if (var13 != -1) {
            aQ = 2;
            if (var2.l != null) {
               for(var4 = 0; var4 < var2.l.length; ++var4) {
                  if ((var2.l[var4] & 255) == var13) {
                     aQ = 0;
                  }
               }
            }

            if (var2.m != null) {
               for(var4 = 0; var4 < var2.m.length; ++var4) {
                  if ((var2.m[var4] & 255) == var13) {
                     aQ = 1;
                  }
               }
            }
         }

         int var14 = var11;

         int var15;
         int var16;
         int var17;
         int var18;
         for(var4 = 0; var4 < var12; ++var4) {
            var15 = var25 + var4 * 4;
            var16 = aJ[var15 + 1] & 255 | (aJ[var15 + 2] & 255) << 8;
            var17 = aJ[var25 + var4 * 4 + 3] & 255;
            var18 = aJ[var25 + var4 * 4 + 0] & 255;
            boolean var19 = g.a(var16, var2, true);
            if (var18 == 1) {
               var19 = !var19;
            }

            if (var19) {
               var14 = var17;
               break;
            }
         }

         if (var6 == 1 && var7 == 2) {
            if (d(var2.b[13]) != -1) {
               g.a((int)915);
            } else if (var14 == var11) {
               if ((var15 = g.b(var2)) == -3) {
                  g.a((int)432);
                  h.P = true;
               } else if (var15 == -2) {
                  g.a(GloftMMN.k(var2.a[1]) + GloftMMN.k(433));
               } else {
                  h.O = true;
               }
            }
         } else if (var14 == var11 && var6 == 1 && var7 == 0 && var8 == 1) {
            if (h.H == 25) {
               g.a((int)904);
            } else {
               byte var28 = var2.b[13];
               var16 = -1;
               if (var28 != -2) {
                  var16 = d(var28);
               }

               if ((var17 = g.b(var2)) != -3 && var17 != -1) {
                  if (var17 != -2 && var16 == -1) {
                     g.a(GloftMMN.k(var2.a[1]) + GloftMMN.k(431));
                  }
               } else {
                  g.a((int)432);
               }

               if (var17 != -3) {
                  h.s[av].a((int)0, (int)0, (int)-1);
                  g.a((int)872);
                  bc[26] = (byte)av;
                  if (bc[0] >= 30) {
                     h.m = 22;
                  } else {
                     h.m = 0;
                  }
               }
            }

            h.z = 0;
            return;
         }

         var25 = c(var6, var7);
         if ((var15 = aJ[var25 + 3] & 255) == 255) {
            var25 = c(var6, -1);
            var15 = aJ[var25 + 3] & 255;
         }

         var25 = g.f(var14);
         var16 = g.as[var25++] & 255;

         for(var4 = 0; var4 < var16; ++var4) {
            var25 = g.h(var14, var4);
            var17 = g.as[var25 + 0] & 255;
            var18 = g.as[var25 + 1] & 255;
            if (var10 >= var17 && var10 <= var18) {
               break;
            }
         }

         var18 = a(var2, g.as, var25 + 10, false, true);
         if (var6 != 3 && var7 != 0 && var18 > 0 && var18 > var3.a[35]) {
            g.a(GloftMMN.k(434) + " " + var18 + "$");
            h.z = 0;
            h.D = 0;
         } else {
            d var29;
            (var29 = h.n[0]).e = -1;
            if (var15 == 60 && var14 != 40) {
               var29.a(48);
            } else {
               var29.a(var15);
            }

            aM = var14;
            aN = var4;
            var25 = g.h(aM, aN);
            aP = g.as[var25 + 3] & 255;
            aO = g.as[var25 + 4] & 255;
            aq = false;
            ar = false;
            var5.e = -1;
            var5.a(0);
            int var20 = (g.as[var25 + 2] & 255) * 255 / 100;
            if (var29.c == 1) {
               var20 *= -1;
            }

            var3.c[0] = var2.c[0] - var20;
            var3.c[1] = var2.c[1] + var20;
            h.z = 11;
         }
      }
   }

   static void d() {
      d var0 = h.n[0];
      if (!aq) {
         g var1 = h.s[av];
         d var2 = h.n[var1.b[5]];
         if (aO == 0) {
            var2.e = -1;
            var2.a(aP);
            aO = -1;
            if (h.y == 11) {
               int var3 = g.h(aM, aN);
               int var4;
               if ((var4 = g.as[var3 + 9] & 255) != 255) {
                  g.x = var1;
                  h.v[17].a(var4);
                  h.v[17].c = 0;
                  a(var2, var0.a > var2.a);
               }
            }
         } else if (aO == -1) {
            if (var2.a()) {
               aq = true;
               var2.e = -1;
               var2.a(0);
            }
         } else {
            --aO;
         }
      }

      if (!ar && var0.a()) {
         ar = true;
         var0.e = -1;
         var0.a(0);
      }

   }

   static void a(d var0, boolean var1) {
      d var3;
      (var3 = h.v[18]).a = var0.a;
      var3.b = var0.b - 16128 + 2816;
      if (var1) {
         var3.a -= 1536;
         var3.c = 1;
      } else {
         var3.a += 1536;
         var3.c = 0;
      }
   }

   static void b(int var0) {
      a var1;
      if ((var1 = h.n[h.s[var0].b[5]].d) != null && var1.w != null) {
         for(int var2 = 0; var2 < var1.w.length; ++var2) {
            if (var1.w[var2] != null) {
               for(int var3 = 0; var3 < var1.w[var2].length; ++var3) {
                  var1.w[var2][var3] = null;
               }

               var1.w[var2] = null;
            }
         }
      }

   }

   static void e() {
      int var1;
      for(var1 = 0; var1 < h.s.length; ++var1) {
         if (h.s[var1] != null) {
            b(var1);
         }
      }

      int var2;
      int var3;
      a var4;
      for(var1 = 0; var1 < 2; ++var1) {
         if ((var4 = h.o[var1]) != null) {
            var2 = 0;
            if (var4.w != null) {
               for(; var2 < var4.w.length; ++var2) {
                  if (var4.w[var2] != null) {
                     for(var3 = 0; var3 < var4.w[var2].length; ++var3) {
                        var4.w[var2][var3] = null;
                     }

                     var4.w[var2] = null;
                  }
               }
            }
         }
      }

      h.t[0].d = null;

      for(var1 = 1; var1 < h.s.length; ++var1) {
         g var0;
         if ((var0 = h.s[var1]) != null) {
            if (var0.e == null) {
               var0.c();
               h.s[var1] = null;
            } else {
               for(var2 = 0; var2 < var0.r.length; ++var2) {
                  var0.r[var2] = 0;
               }
            }
         }
      }

      for(var1 = 0; var1 < h.p.length; ++var1) {
         if (h.p[var1] != null) {
            for(var2 = 0; var2 < h.p[var1].length; ++var2) {
               if ((var4 = h.p[var1][var2]) != null && var4.w != null) {
                  for(var3 = 0; var3 < var4.w.length; ++var3) {
                     if (var4.w[var3] != null) {
                        for(int var5 = 0; var5 < var4.w[var3].length; ++var5) {
                           var4.w[var3][var5] = null;
                        }
                     }
                  }
               }
            }
         }
      }

   }

   private static void s() {
      bk = false;
      int var3 = h.s[0].a[0];
      g.j[0] = 0;
      g.j[1] = 0;
      h.s[0].a(7 + h.s[0].a[0] * 2, 0, true);
      g.j[var3] = 1;

      int var0;
      for(var0 = 0; var0 < h.s.length; ++var0) {
         if (h.s[var0] != null) {
            if (bc[25] != var0) {
               h.s[var0].b[19] = 0;
            }

            h.s[var0].b[20] = 0;
            h.s[var0].s = false;
         }
      }

      int var5 = 1;

      g var2;
      for(int var6 = 0; var6 < 2; ++var6) {
         for(var0 = 0; var0 < f; ++var0) {
            short var4;
            if ((var4 = e[var0][0]) == 90 || var4 == 92) {
               if (var4 == 92) {
                  if (var6 == 0 && bc[25] > 0 && bc[26] <= 0) {
                     var2 = h.s[bc[25]];
                     if (bc[24] == K) {
                        a(var0, var2.c);
                        var2.a(var2.c[0], var2.c[1]);
                        var2.b[4] = var2.b[2];
                        var2.a((int)0, (int)-1, (int)-1);
                        var2.a(7 + var2.a[0] * 2, bc[25], true);
                        if (var2.b[19] == 0) {
                           ai = 870;
                        }

                        var2.b[19] = 1;
                        var2.b[20] = 1;
                        var2.b[12] = 0;
                        var2.A = false;
                     }
                  } else if (bc[26] > 0 && var6 == 0) {
                     var2 = h.s[bc[26]];
                     if (bc[0] >= 30 && (K == 22 || K == 34 || K == 23) || bc[0] < 30 && (K == 0 || K == 1)) {
                        a(var0, var2.c);
                        var2.a(var2.c[0], var2.c[1]);
                        var2.b[4] = var2.b[2];
                        var2.a((int)0, (int)-1, (int)-1);
                        var2.a(7 + var2.a[0] * 2, bc[26], true);
                        if (var2.b[19] == 0) {
                           ai = 870;
                        }

                        var2.b[19] = 1;
                        var2.b[20] = 1;
                        var2.b[12] = 0;
                        var2.A = false;
                        bk = true;
                     } else {
                        var2.b[19] = 0;
                        var2.b[20] = 0;
                     }
                  }
               } else {
                  short var7 = e[var0][9];
                  short var8 = e[var0][10];
                  boolean var9 = var7 < 2 || g.a(var7 - 2, (g)null, false);
                  boolean var10 = var8 < 2 || g.a(var8 - 2, (g)null, false);
                  if (var9 && var10) {
                     int var1;
                     for(var1 = 1; var1 < h.s.length && ((var2 = h.s[var1]) == null || var2.e == null || (var2.a[43] == 127 || e[var0][4] != var2.b[13]) && (var2.e[0] != K || (var2.e[1] & 255) != var0)); ++var1) {
                     }

                     if (var1 != h.s.length) {
                        if (var6 == 0 && var1 != bc[25]) {
                           h.s[var1].b[20] = 1;
                           h.s[var1].b[14] = (byte)var0;
                           h.s[var1].b[3] = 0;
                           h.s[var1].b[18] = -1;
                           a(var0, h.s[var1].c);
                           h.s[var1].a(h.s[var1].c[0], h.s[var1].c[1]);
                           h.s[var1].a((int)e[var0][3], (int)e[var0][5], (int)-1);
                           h.s[var1].b[4] = -1;
                           h.s[var1].a(7 + h.s[var1].a[0] * 2, var1, true);
                        }
                     } else if (var6 == 1) {
                        while(var5 < h.s.length && h.s[var5] != null) {
                           ++var5;
                        }

                        (var2 = new g(-1, var5, e[var0][4], false, true)).b[14] = (byte)var0;
                        h.s[var5] = var2;
                        var2.b[6] = (byte)var5;
                        a(var0, h.s[var5].c);
                        h.s[var5].a(h.s[var5].c[0], h.s[var5].c[1]);
                        var2.a((int)e[var0][3], (int)e[var0][5], (int)-1);
                     }
                  }
               }
            }
         }
      }

      c(-1);
      g.a(true);
      if (bc[25] > 0 && bc[24] != K && (var2 = h.s[bc[25]]).b[19] == 1) {
         if (bc[26] == bc[25] && (bc[0] >= 30 && (K == 22 || K == 34 || K == 23) || bc[0] < 30 && (K == 0 || K == 1))) {
            bc[25] = -1;
            return;
         }

         var2.b[19] = 0;
         bc[25] = -1;
         ai = 880;
      }

   }

   static void c(int var0) {
      for(int var4 = 1; var4 < h.s.length; ++var4) {
         g var8;
         if ((var8 = h.s[var4]) != null && var8.b()) {
            int var1 = var8.c[0] & -256;
            int var2 = var8.c[1] & -256;
            int var5 = var8.b[2] & 255;
            int var6;
            if ((var6 = aL[1 + var5 * 5 + 2] & 255) != 255) {
               g var7 = null;
               int var3;
               if ((var3 = (b(var1 + 256, var2 - 256) & 15872) >> 9) != 0) {
                  var7 = h.s[var3];
               } else if ((var3 = (b(var1 - 256, var2 + 256) & 15872) >> 9) != 0) {
                  var7 = h.s[var3];
               }

               if (var7 != null && (var0 == -1 || (var8.b[6] & 255) == var0 || (var7.b[6] & 255) == var0)) {
                  int var9 = aL[1 + var5 * 5 + 3] & 255;
                  int var10 = 255 * var9 / 100;
                  var7.a(var8.c[0] + var10, var8.c[1] - var10);
                  var8.a((int)var5, (int)0, (int)var6);
                  var7.a((int)var6, (int)1, (int)var5);
                  if (var7.y != null) {
                     d var10000 = var7.y;
                     var10000.a -= (44 * var9 << 8) / 200;
                  }

                  var8.b[12] = var7.b[6];
                  var7.b[12] = var8.b[6];
               }
            }
         }
      }

   }

   static int c(int var0, int var1) {
      int var2 = 0;

      do {
         do {
            int var3 = aJ[var2 + 3] & 255;
            int var4 = 0;
            if (var0 != 0 || var1 != -1) {
               for(var2 += 4; var4 < var3; ++var4) {
                  if (var0 == 0 && var1 != -1 && var4 == var1) {
                     return var2;
                  }

                  int var5 = aJ[var2 + 5] & 255;
                  int var6 = aJ[var2 + 6] & 255;
                  var2 += 7;

                  for(var2 += var6 * 4; var5 > 0; --var5) {
                     var2 += 3;
                     var6 = aJ[var2] & 255;
                     var2 += 1 + var6 * 4;
                  }
               }

               --var0;
            }
         } while(var0 > 0);
      } while(var0 >= 0 && var1 != -1);

      return var2;
   }

   static void f() {
      aJ = GloftMMN.j(1);
   }

   static void g() {
      aL = GloftMMN.j(3);
   }

   static void h() {
      aK = GloftMMN.j(2);
   }

   static int d(int var0, int var1) {
      int var2 = aK[1 + var0 * 2] & 255 | (aK[1 + var0 * 2 + 1] & 255) << 8;
      int var3 = aK[0] & 255;
      int var4 = aK[1 + var3 * 2] & 255;
      ++var2;
      int var8 = var1;
      if (var1 < 0) {
         var8 = var4;
      }

      for(int var5 = 0; var5 < var8; ++var5) {
         byte var6;
         int var7;
         if ((var7 = (var6 = aK[var2++]) & 3) == 0) {
            ++var2;
         } else if (var7 == 1) {
            var2 += 2;
         } else if (var7 == 2) {
            var2 += (var6 & 252) >> 2;
         }
      }

      return var2;
   }

   static int d(int var0) {
      int var1 = aK[1 + var0 * 2] & 255 | (aK[1 + var0 * 2 + 1] & 255) << 8;
      return aK[var1];
   }

   static void b(Graphics var0, int var1) {
      if (h.D == 2 && var1 == 4) {
         b(var0, true);
      } else {
         if (au >= 0) {
            h(au);
         } else if (h.D == 0) {
            h.E[0] = -1;
            h.E[1] = -1;
            h.E[2] = -1;
            g var2 = h.s[0];
            g var3 = h.s[av];
            d var4 = h.n[var3.b[5]];
            a(var2, var3);
            a(var3, var2);
            var4.a(0);
            int var5;
            if ((var5 = var3.b[12] & 255) != 0) {
               h.n[h.s[var5].b[5] & 255].a(0);
            }

            var3.A = true;
         }

         h.z = (byte)var1;
         h.v[0].a(15);
         h.B = 0;
      }
   }

   static void c(Graphics var0, int var1) {
      boolean var3 = false;
      byte var4 = -1;
      d var7 = h.v[0];
      d var8 = h.v[3];
      d var9 = h.v[4];
      var7.a = 17408;
      var7.b = 65280;
      if (var7.e == 15 && var7.g == 0) {
         n();
         a(var0);
         o();
      }

      if (var7.e == 15) {
         var7.d.a((Graphics)var0, 5, 0, var7.a >> 8, var7.b >> 8, 0, 0, 0);
         var7.d.a((Graphics)var0, 6, 0, var7.a >> 8, var7.b >> 8, 0, 0, 0);
         var7.a(var0);
         if (var7.a()) {
            var7.e = -1;
         } else {
            var7.b();
         }
      } else {
         int var17;
         if (var1 == 3) {
            h.C = aY;
         } else if (var1 == 4) {
            if (h.D == 0) {
               h.C = 4;
            } else if (h.D == 1) {
               var17 = c(var4 = h.E[h.D - 1], -1);
               h.C = aJ[var17 + 3];
            }
         }

         var7.a(17 + h.B);
         int var10;
         boolean var11;
         if ((var11 = (var10 = a(var7)) != 16 && var10 != 17 && var10 != 18 && var10 != 19) || h.aa) {
            n();
            a(var0);
            o();
            if (var11) {
               h.aa = true;
            } else {
               h.aa = false;
            }
         }

         var7.d.a((Graphics)var0, 5, 0, var7.a >> 8, var7.b >> 8, 0, 0, 0);
         var7.d.a((Graphics)var0, 6, 0, var7.a >> 8, var7.b >> 8, 0, 0, 0);
         var8.a(4);
         if (h.y != 34 && h.y != 16) {
            var8.a = var7.a;
            var8.b = var7.b;
            var8.a(var0);
            var8.b();
         }

         h.b();

         for(int var2 = h.C - 1; var2 >= 0; --var2) {
            int var12 = (var7.a >> 8) + var7.d.b(var10, var2);
            int var13 = (var7.b >> 8) + var7.d.c(var10, var2);
            int[] var14 = new int[]{0, 0, 0, 0};
            var7.d.a((int[])var14, 14, 0, var12 << 8, var13 << 8, 0, 0, 0);
            byte var15 = 4;
            switch(var2) {
            case 0:
               var15 = 4;
               break;
            case 1:
               var15 = 6;
               break;
            case 2:
               var15 = 7;
               break;
            case 3:
               var15 = 5;
            }

            if (var2 == h.B) {
               h.a(var14[0] >> 8, var14[1] >> 8, var14[2] - var14[0] >> 8, var14[3] - var14[1] >> 8, 0, 0, 3, 0, var15, 0);
            } else {
               h.a(var14[0] >> 8, var14[1] >> 8, var14[2] - var14[0] >> 8, var14[3] - var14[1] >> 8, var15, 0, 0, 0, var15, 0);
            }

            var7.d.a((Graphics)var0, 14, 0, var12, var13, 0, 0, 0);
            if (var2 == h.B) {
               var9.a = var12 << 8;
               var9.b = var13 << 8;
               if (h.y == 34) {
                  var9.a(9);
               } else {
                  var9.a(8);
               }

               var9.a(var0);
            }

            int var5 = -1;
            int var6 = -1;
            if (var1 == 3) {
               int var16;
               if ((var16 = aX[var2]) == -1) {
                  var5 = 21;
                  var6 = 0;
               } else {
                  var5 = g(var6 = i(var16, 5), 1);
               }
            } else if (h.D == 0) {
               var17 = c(var2, -1);
               var5 = aJ[var17 + 2];
               var6 = aJ[var17 + 0];
            } else if (h.D == 1) {
               var17 = c(var4, var2);
               var5 = aJ[var17 + 2];
               var6 = aJ[var17 + 0];
            }

            h.u[1].a((Graphics)var0, var5, 0, var12, var13, 0, 0, 0);
            if (var2 == h.B) {
               int[] var18;
               if ((var18 = var9.c(0)) != null) {
                  h.M.a(var0, GloftMMN.k(var6), var18[0], var18[1], 20, 1);
               }

               if (!var9.a()) {
                  var9.b();
               }
            }
         }

      }
   }

   static void b(Graphics var0, boolean var1) {
      d var2 = h.v[0];
      d var3 = h.v[4];
      byte var4 = h.E[h.D - 2];
      byte var5 = h.E[h.D - 1];
      int var6 = c(var4, var5);
      int var7 = aJ[var6 + 5] & 255;
      int var8 = aJ[var6 + 6] & 255;
      var6 += 7 + var8 * 4;
      if (var1) {
         if (var7 <= 0) {
            a(var0, false);
         } else {
            h.y = 16;
            h.B = 0;
         }
      } else {
         byte var9 = h.B;
         h.B = (byte)var5;
         --h.D;
         c(var0, 4);
         h.y = 16;
         ++h.D;
         h.B = var9;
         var0.setClip(0, 0, 240, 400);
         var0.setColor(0);
         boolean var11 = false;
         int[] var15 = new int[var7];

         int var10;
         for(var10 = 0; var10 < var7; ++var10) {
            var15[var10] = aJ[var6 + 0] | aJ[var6 + 0 + 1] << 8;
            var8 = aJ[var6 + 3] & 255;
            var6 += 4 + var8 * 4;
         }

         int[] var16 = var3.c(0);
         h.b();

         for(var10 = var7 - 1; var10 >= 0; --var10) {
            byte var13 = 11;
            if (var10 == h.B) {
               var13 = 12;
            }

            var2.d.a((Graphics)var0, var13, 0, var3.a >> 8, (var3.b >> 8) - 18 * (var7 - 1 - var10), 0, 0, 0);
            int var20 = var16[0];
            int var12 = var16[1] - 18 * (var7 - var10) + 2;
            h.M.a(var0, GloftMMN.k(var15[var10]), var20, var12, 20, 1);
            int var17 = var3.a >> 8;
            int var18 = (var3.b >> 8) - 18 * (var7 - 1 - var10);
            int[] var19 = new int[]{0, 0, 0, 0};
            var2.d.a((int[])var19, 11, 0, var17 << 8, var18 << 8, 0, 0, 0);
            h.a(var19[0] >> 8, var19[1] >> 8, var19[2] - var19[0] >> 8, var19[3] - var19[1] >> 8, 29, var10, 3, 0, 29, var10);
         }

      }
   }

   static void e(int var0, int var1) {
      int var2 = au;
      int var3 = av;
      au = -1;
      av = -1;
      int var4 = var0 >> 8;
      int var5 = var1 >> 8;
      int var16 = 25600000;
      g var19 = h.s[0];
      int var17 = var0 + var19.o[0];
      int var18 = var1 + var19.o[1];
      var17 >>= 8;
      var18 >>= 8;

      int var8;
      short var12;
      d var21;
      for(int var6 = var4 - 1; var6 <= var4 + 1; ++var6) {
         for(int var7 = var5 - 1; var7 <= var5 + 1; ++var7) {
            int var9 = b(var6 << 8, var7 << 8);
            int var13 = (var6 << 8) + 127;
            int var14 = (var7 << 8) + 127;
            int var15;
            if ((var9 & 15872) != 0) {
               int var20 = (var9 & 15872) >> 9;
               if ((h.s[var20].b[11] & 255) != 6) {
                  var21 = h.n[h.s[var20].b[5] & 255];
                  if (h.s[var20].s && var21.a >> 8 > 0 && var21.b >> 8 < 315) {
                     var15 = ((var13 - var0) * (var13 - var0) >> 8) + ((var14 - var1) * (var14 - var1) >> 8);
                     if (var6 == var17 && var7 == var18) {
                        av = var20;
                        au = -1;
                        var6 = var4 + 2;
                        var7 = var5 + 2;
                     } else if (var15 <= var16) {
                        av = var20;
                        au = -1;
                        var16 = var15;
                     }
                  }
               }
            }

            if (var6 != var4 + 2 && var7 != var5 + 2 && (var9 & 3) == 2) {
               int var10 = (var9 & 508) >> 2;

               for(var8 = 0; var8 < 4; ++var8) {
                  byte var11;
                  if ((var11 = l[var10 * 4 + var8]) != -1) {
                     int var28 = var11 & 255;
                     if ((var12 = e[var28][0]) < 90 && b(var12, 1, e[var28][3]) <= 6) {
                        f(var28);
                        if (aY > 0 && (var12 != 57 || h.H != 28 && h.H != 23 && h.H != 31)) {
                           if (var6 == var17 && var7 == var18) {
                              au = var28;
                              av = -1;
                              var6 = var4 + 2;
                              var7 = var5 + 2;
                              break;
                           }

                           if (av == -1 && (var15 = ((var13 - var0) * (var13 - var0) >> 8) + ((var14 - var1) * (var14 - var1) >> 8)) <= var16) {
                              au = var28;
                              av = -1;
                              var16 = var15;
                           }
                        }
                     }
                  }
               }
            }
         }
      }

      d var29 = h.v[1];
      var21 = h.v[2];
      String var23 = null;
      if (au == 38 && f == 56) {
         au = -1;
      }

      boolean var22;
      if (av != -1) {
         var22 = var3 != av;
      } else {
         if (au == -1) {
            return;
         }

         var22 = var2 != au;
         f(au);
         int var24 = e[au][3];
         int var25 = -1;
         int var26 = b(var12 = e[au][0], 3, -1);

         for(var8 = 0; var8 < var26; ++var8) {
            int var27 = b(var12, 4, var8);
            if (var24 < var27) {
               var25 = var8;
               break;
            }

            var24 -= var27;
         }

         var23 = l(e[au][0], var25);
      }

      if (var22) {
         var29.e = -1;
         var29.a(0);
         var21.e = -1;
         aw = 0;
      } else {
         if (aw >= 300 && var21.e == -1) {
            var21.a(2);
         }

         if (var21.e == 2 && var21.a()) {
            var21.a(3);
         }

         aw = (int)((long)aw + h.aH);
      }

      i();
      if (var23 != null) {
         a((String)var23, 0);
      }

   }

   static void a(Graphics var0, d var1, boolean var2, boolean var3, boolean var4, g var5) {
      d var6 = h.v[1];
      d var7 = h.v[2];
      var0.setClip(0, 75, 240, 240);
      if (var2 && g.p > 500L) {
         int[] var8 = var1.c();
         if (var4) {
            var7.a = var1.a;
            var7.b = var1.b - 16128;
         } else {
            var7.a = var8[2] + var8[0] >> 1;
            var7.b = var8[1];
         }

         h.bb[0] = var7.a >> 8;
         h.bb[1] = var7.b >> 8;
         h.bc = true;
         if (var7.e == 0) {
            var7.a(3);
         }

         if (var7.e >= 0) {
            var7.a(var0);
            var7.b();
         }
      } else if (var5 != null && d(var5.b[13]) >= 0) {
         var0.setClip(0, 75, 240, 240);
         h.u[1].a((Graphics)var0, 87, 0, var1.a >> 8, var5.d[1], 0, 0, 0);
      }

      if (var3) {
         d var9 = h.n[0];
         var6.a = var9.a;
         var6.b = var9.b - 16128;
         if (var6.b < 21248) {
            var6.a = var9.a + -4096;
            var6.b = var9.b - 8064;
         }

         var6.a(var0);
         if (var6.e == 1 && var6.a()) {
            var6.e = -1;
            var6.a(1);
         }

         if (!var6.a()) {
            var6.b();
         }

         h.bd[0] = var6.a >> 8;
         h.bd[1] = var6.b >> 8;
         h.be = true;
      }

   }

   static boolean f(int var0, int var1) {
      int var3 = b(var0, var1);
      if (bc[31] == 0 && (var3 & 3) == 2) {
         int var4 = (var3 & 508) >> 2;

         for(int var2 = 0; var2 < 4; ++var2) {
            byte var5;
            if ((var5 = l[var4 * 4 + var2]) != -1) {
               int var9 = var5 & 255;
               short var7;
               if (e[var9][0] == 94 && (var7 = e[var9][4]) > -2) {
                  if (aR != var9) {
                     aR = var9;
                     au = -1;
                     av = -1;
                     d var8;
                     (var8 = h.v[1]).e = -1;
                     var8.a(1);
                  }

                  i();
                  int var10 = var7 & 255;
                  var2 = 141 + var10;
                  if (var10 > 127) {
                     var2 = 400;
                  }

                  if (var2 == 169) {
                     var2 = 918;
                  }

                  a((String)GloftMMN.k(var2), 1);
                  return true;
               }
            }
         }
      }

      aR = -1;
      return false;
   }

   static void a(Graphics var0, boolean var1, boolean var2) {
      if (h.X != 1) {
         var0.setClip(0, 0, 240, 400);
         d var3;
         int var4 = (var3 = h.v[7]).a;
         int var5 = var3.b;
         int var6 = var3.e;
         var3.a = 30720;
         var3.b = 51200;
         if (var1) {
            var0.setColor(0);
            var0.fillRect(0, 0, 240, 75);
            g.H = -1;
            var3.a(24);
            var3.a(var0);
         }

         if (var2) {
            var0.setColor(0);
            var0.fillRect(0, 314, 240, 85);
            var3.a(25);
            var3.b = 65280;
            var3.a(var0);
         }

         var3.a = var4;
         var3.b = var5;
         var3.e = var6;
      }
   }

   static void a(String var0, int var1) {
      aS.addElement(Integer.toString(var1) + var0);
   }

   static void b(Graphics var0) {
      if (aS != null) {
         if (h.k) {
            g.p = 1000L;
            be = null;
         }

         var0.setClip(0, 315, 240, 85);
         d var1;
         (var1 = h.v[7]).a = 30720;
         var1.b = 51200;
         if (aS.size() <= 0) {
            if (be != null) {
               var1.a(30);
               var1.a(var0);
               be = null;
            }

         } else {
            String var2 = (String)aS.elementAt(0);
            if (h.h != 8 || (be == null || be.compareTo(var2) != 0) && g.p >= 700L) {
               byte var4 = 0;
               byte var5 = 3;
               if (aR != -1) {
                  var1.a(27);
                  var1.a(var0);
                  var5 = 2;
                  var4 = 1;
               } else if (au != -1) {
                  var1.a(29);
                  var1.a(var0);
               } else {
                  var1.a(26);
                  var1.a(var0);
               }

               int[] var3 = var1.c(var5);
               h.M.a(var0, var2.substring(1), var3[0], var3[1], 20, var4);
               be = var2;
            }

         }
      }
   }

   static void i() {
      if (aS.size() > 0) {
         aS.removeElementAt(0);
      }

   }

   private static String m(int var0, int var1) {
      short var2;
      if (var1 == 0 && var0 == 0) {
         var0 = 12;
         var2 = 840;
      } else if (var0 == 0) {
         var0 += 12;
         var2 = 839;
      } else if (var0 >= 1 && var0 <= 11) {
         var2 = 839;
      } else if (var0 >= 12 && var0 < 13) {
         var2 = 840;
      } else {
         var0 -= 12;
         var2 = 840;
      }

      String var3 = GloftMMN.k(var2);
      return var0 < 10 ? "0" + var0 + var3 : var0 + var3;
   }

   static String a(long var0, boolean var2) {
      long var5 = var0 / 3600L % 24L;
      long var7 = var0 / 60L % 60L;
      String var9 = "" + var7;
      if (var7 < 10L) {
         var9 = "0" + var9;
      }

      String var10;
      if (var2) {
         int var11 = (var10 = m((int)var5, (int)var7)).length();
         var10 = var10.substring(0, var11 - 2) + ":" + var9 + var10.substring(var11 - 2, var11);
      } else {
         var10 = "" + var5;
         if (var5 < 10L) {
            var10 = "0" + var5;
         }

         var10 = var10 + ":" + var9;
      }

      return var10;
   }

   static boolean j() {
      long var2;
      return (var2 = aT / 1000L * 60L / 3600L % 24L) >= 21L || var2 <= 4L;
   }

   static boolean k() {
      long var2;
      return (var2 = aT / 1000L * 60L / 3600L % 24L) >= 17L && var2 < 21L;
   }

   static boolean l() {
      long var2;
      return (var2 = aT / 1000L * 60L / 3600L % 24L) >= 5L && var2 <= 8L;
   }

   static void c(Graphics var0, boolean var1) {
      if (bm == -1) {
         if ("LANG".compareTo("EN") == 0) {
            bm = 1;
         } else {
            bm = 0;
         }
      }

      if (!var1) {
         var0.setClip(0, 0, 240, 400);
         h.v[7].a(24);
         int[] var2 = h.v[7].c(1);
         var0.setColor(0);
         var0.fillRect(var2[0], var2[1], 120, 15);
         String var3 = a(aT / 1000L * 60L, bm == 1);
         h.M.a(var0, var3, var2[0], var2[1], 20, 0);
      }

      long var13;
      long var4 = (var13 = aT / 1000L * 60L) / 3600L % 24L;
      long var6 = var13 / 60L % 60L;
      if (h.ai == 2 && var4 == 0L && bc[2] == 23) {
         h.aj = true;
      }

      bc[2] = (byte)((int)var4);
      if (var6 != (long)aV) {
         for(int var8 = 0; var8 < 1; ++var8) {
            long var10;
            if ((var10 = var6 - (long)aV) < 0L) {
               var10 += 60L;
            }

            for(int var12 = 0; var12 < g.D; ++var12) {
               if (g.q[var12] != -1L) {
                  long[] var10000 = g.q;
                  var10000[var12] -= var10;
                  if (g.q[var12] < 0L) {
                     g.q[var12] = 0L;
                  }
               }
            }
         }
      }

      aV = (int)var6;
   }

   static String e(int var0) {
      String var1 = "";
      String var3;
      int var4;
      int var5;
      if (var0 > 1000000000) {
         var4 = (var3 = Integer.toString((int)((long)var0 * 100L / 1000000000L))).length();

         for(var5 = 0; var5 < var4; ++var5) {
            var1 = var1 + var3.substring(var5, var5 + 1);
            if (var5 == 0) {
               var1 = var1 + ",";
            }
         }

         var1 = var1 + "<";
      } else if (var0 > 1000000) {
         var4 = (var3 = Integer.toString((int)((long)var0 * 100L / 1000000L))).length();

         for(var5 = 0; var5 < var4; ++var5) {
            var1 = var1 + var3.substring(var5, var5 + 1);
            if (var5 == 0) {
               var1 = var1 + ",";
            }
         }

         var1 = var1 + ">";
      } else {
         String var2;
         int var6 = (var2 = Integer.toString(var0)).length();

         for(var4 = 0; var4 < var2.length(); ++var4) {
            var1 = var1 + var2.substring(var4, var4 + 1);
            if (var4 != var6 - 1 && (var6 - 1 - var4) % 3 == 0) {
               var1 = var1 + ",";
            }
         }
      }

      return var1;
   }

   static void d(Graphics var0, boolean var1) {
      g var2 = h.s[0];
      d var3 = h.n[0];
      var0.setClip(0, 0, 240, 400);
      if (var1 || var2.a[35] != g.H || h.k || bn) {
         d var4;
         int var5 = (var4 = h.v[7]).a;
         int var6 = var4.b;
         var4.a = 30720;
         var4.b = 51200;
         var4.a(24);
         int[] var7 = var4.c(2);
         var0.setColor(0);
         var0.fillRect(var7[0], var7[1], 240 - var7[0], 15);
         d var8;
         (var8 = h.v[11]).a = var7[0] << 8;
         var8.b = var7[1] << 8;
         if (var1) {
            var8.a(80);
         } else {
            var8.a(79);
         }

         var8.a(var0);
         var8.b();
         int[] var9 = var4.c(3);
         String var10;
         if (var1) {
            int var11 = bo[70];
            int var13;
            var10 = e(Math.abs(var13 = bo[71] - var11));
            if (var13 < 0) {
               var10 = "-" + var10;
            } else {
               var10 = "+" + var10;
            }

            d var14;
            (var14 = h.v[12]).a = var8.a;
            var14.b = 19200;
            if (var13 < 0) {
               var14.a(82);
            } else {
               var14.a(81);
               int var15 = (h.K % 6 << 8) / 5;
               var14.a = var3.a + (var8.a - var3.a >> 8) * var15;
               var14.b = var3.b - 16128 + (75 - ((var3.b >> 8) - 63)) * var15;
            }

            if (h.h != 18) {
               var0.setClip(0, 0, 240, 400);
               var14.a(var0);
               var14.b();
            }
         } else {
            var10 = e(var2.a[35]);
            g.H = var2.a[35];
         }

         h.M.a(var0, var10, var9[0], var9[1], 24, 0);
         var4.a = var5;
         var4.b = var6;
      }

      bn = false;
   }

   static int a(g var0, byte[] var1, int var2, boolean var3, boolean var4) {
      int var5;
      if (var0 == h.s[0]) {
         if (bo == null) {
            bo = new int[var0.a.length * 2];
         }

         for(var5 = 0; var5 < var0.a.length; ++var5) {
            bo[var5 * 2 + 0] = var0.a[var5];
         }
      }

      for(var5 = 0; var5 < aW.length; ++var5) {
         aW[var5] = null;
      }

      int var6 = var1[var2 + 0] & 255;
      if (var3) {
         return var2 + 1 + var6 * 2;
      } else {
         for(var5 = 0; var5 < var6; ++var5) {
            byte var7 = var1[var2 + 1 + var5 * 2 + 0];
            int var8 = var1[var2 + 1 + var5 * 2 + 1];
            if (var7 == 35) {
               var8 *= 5;
            }

            if (!var4) {
               aW[var5] = GloftMMN.k(g.d(var7, 0));
               if (var8 != 0) {
                  if (var8 > 0) {
                     aW[var5] = "+" + aW[var5];
                  } else {
                     aW[var5] = "-" + aW[var5];
                  }
               }

               aW[var5] = aW[var5] + "#" + var7;
               int var9 = var0.a[var7];
               var0.c(var7, var8);
               int var10 = var0.a[var7];
               if (var0.b[6] == 0) {
                  for(int var11 = 0; var11 < g.B.length; ++var11) {
                     if (g.B[var11] == var7) {
                        int var13 = g.e(var7, var9);
                        int var14 = g.e(var7, var10);
                        if (var10 > var9 && var13 != var14) {
                           if (var0.b[17] == 0) {
                              var0.b[17] = (byte)var7;
                           }
                           break;
                        }
                     }
                  }
               }
            } else if (var7 == 35) {
               if (var8 < 0) {
                  return -var8;
               }

               return 0;
            }
         }

         if (var0 == h.s[0]) {
            for(var5 = 0; var5 < var0.a.length; ++var5) {
               bo[var5 * 2 + 1] = var0.a[var5];
            }
         }

         return -1;
      }
   }

   static int g(int var0, int var1) {
      for(int var3 = 0; var3 < am; ++var3) {
         int var2 = ao[var3];
         if ((an[var2 + 0] & 255) == var0) {
            if (var1 == -1) {
               return var3;
            }

            if (var1 != 2 && var1 != 4) {
               return an[var2 + var1] & 255;
            }

            return an[var2 + var1] & 255 | (an[var2 + var1 + 1] & 255) << 8;
         }
      }

      return -1;
   }

   static void f(int var0) {
      if (aX == null) {
         aX = new int[4];
      }

      aY = 0;
      short var3 = e[var0][0];
      int var4 = 0;

      for(int var1 = 0; var1 < as; ++var1) {
         int var2;
         if (((var2 = at[var4 + 0] & 255) == 255 || var2 == h.H) && (at[var4 + 1] & 255) == var3) {
            int var8 = c[var0].e;
            int var9 = at[var4 + 2] & 255 | (at[var4 + 3] & 255) << 8;

            for(int var10 = 0; var10 < 16; ++var10) {
               if ((var9 & 1 << var10) != 0) {
                  int var7 = h(var3, var10);
                  int var6 = b(var3, 4, var10);
                  if (var8 >= var7 && var8 < var7 + var6) {
                     aX[aY++] = var1;
                     var10 = 16;
                     if (aY == 3 && var3 == 85) {
                        var1 = as;
                     }
                  }
               }
            }
         }

         var4 += 11;
      }

   }

   static int h(int var0, int var1) {
      int var2 = 0;

      for(int var4 = 0; var4 < var1; ++var4) {
         var2 += b(var0, 4, var4);
      }

      return var2;
   }

   private static int n(int var0, int var1) {
      short var2;
      int var3 = b(var2 = e[var0][0], 3, -1);
      int var4 = 0;

      for(int var5 = 0; var5 < var3 - 1; ++var5) {
         if (var1 >= h(var2, var5) && var1 < h(var2, var5 + 1)) {
            var4 = var5;
            break;
         }
      }

      return var4;
   }

   static int i(int var0, int var1) {
      return at[var0 * 11 + var1] & 255;
   }

   static void a(int var0, boolean var1) {
      if (!var1) {
         h.N[2] = 0;
      }

      ax = (byte)var0;
      int var2 = i(var0, 5);
      boolean var3 = bc[26] > 0 || bc[25] > 0 || bc[31] == 1;
      if (var2 == 5) {
         if ((bc[0] & 255) >= 1 && !var3) {
            h.a(18);
            h.y = 36;
            h.z = 37;
            h.i = 0;
         } else {
            g.a((int)873);
            h.y = 0;
         }
      } else if (var2 == 62) {
         if (var3) {
            g.a((int)873);
            h.y = 0;
         } else {
            h.a(18);
            h.y = 36;
            h.z = 58;
            h.i = 0;
         }
      } else if (var2 == 64) {
         if (h.s[0].a[22] > 40 && bc[30] == 1) {
            h.a(18);
            h.y = 36;
            h.z = 61;
            h.i = 0;
         } else {
            g.a((int)354);
            h.y = 0;
         }
      } else {
         boolean var4 = true;
         if (var2 == 3 || var2 == 1 || var2 == 4) {
            if (bb[0] != 0) {
               if (GloftMMN.b(0, 4)) {
                  GloftMMN.d(0);
               }

               h.y = 0;
               bb[0] = 0;
               c();
               h.k = true;
               return;
            }

            GloftMMN.e();
            GloftMMN.c(4);
            var4 = false;
         }

         if (var2 == 56 && bc[12] == 1) {
            g.a((int)905);
            h.y = 0;
         } else if (bk && var2 == 10) {
            g.a((int)913);
            h.y = 0;
         } else {
            d var6;
            (var6 = h.n[0]).a(i(ax, 6));
            var6.c = 0;
            aF = c[au];
            ay = (byte)((i(ax, 8) & 254) >> 1);
            az = (ay & 255) * var6.b(var6.e);
            aA = g(i(ax, 5), 2);
            int var7 = (az << 8) / 12;
            aU = (long)((aA * 100 << 8) / var7);
            aB = aT + (long)aA * 60L * 1000L / 60L;
            aC = 0;
            aD = (i(ax, 8) & 1) == 1;
            aG = (byte)i(ax, 9);
            int var8;
            int var15;
            if (aG == -1) {
               int[] var9;
               if ((var9 = g(au)) != null && var4) {
                  var6.a = var9[0] << 8;
                  var6.b = var9[1] << 8;
               }

               var8 = au;
            } else {
               int[] var10 = g(var15 = r());
               var6.a = var10[0] << 8;
               var6.b = var10[1] << 8;
               var8 = var15;
            }

            h.ae[0] = h.s[0].c[0];
            h.ae[1] = h.s[0].c[1];
            b((var6.a >> 8) - 0 + C, (var6.b >> 8) - 75 + D, h.s[0].c);
            int var12;
            int var16;
            if (!a(var15 = b(h.s[0].c[0], h.s[0].c[1]), h.s[0].c[0], h.s[0].c[1], -1, false) && (var15 & 3) != 1) {
               var16 = au;
               int[] var11;
               var12 = (var11 = c[var16].c())[0] + (var11[2] - var11[0]) / 2;
               if (var6.a > var12) {
                  var6.c = 1;
               } else {
                  var6.c = 0;
               }
            } else {
               var6.c = c[var8].c;
            }

            if (var2 == 34) {
               if (c[var8].c == 0) {
                  var6.c = 1;
               } else {
                  var6.c = 0;
               }
            }

            if (var2 == 53) {
               var6.c = 0;
            }

            if (var2 == 41) {
               var6.c = 0;
            }

            if (var2 == 42) {
               var6.c = 1;
            }

            if (var2 == 23 && h.H == 11) {
               var6.c = 1;
            }

            if (var2 == 31 && h.H == 11) {
               var6.c = 0;
            }

            aH = aF.e;
            var16 = i(ax, 4);
            int var17 = c[au].e;
            var12 = n(au, var17);
            int var13 = h(e[au][0], var12);
            aF.a(var13 + var16);
            h.z = 5;
            aE = 1;
            int var14;
            if ((var14 = i(ax, 10) & 255) != 255) {
               h.t[0].a = var6.a;
               h.t[0].b = var6.b;
               h.t[0].c = var6.c;
               h.t[0].a(var14);
            }

         }
      }
   }

   static void m() {
      d var0 = h.n[0];
      boolean var1 = false;
      if (ay > 0 && ay < 127) {
         if (var0.a()) {
            ++aC;
         }

         if (aC == ay) {
            var1 = true;
         }

         if (i(ax, 5) == 20 && GloftMMN.f && h.X != 1) {
            try {
               if (bp == null) {
                  System.out.println("hiiiiiiiiiiii" + h.h + " :: " + h.y);
                  bp = Manager.createPlayer("".getClass().getResourceAsStream("/sfx_shower.wav"), "audio/x-wav");
               }

               bp.realize();
               bp.prefetch();
               bp.start();
            } catch (Exception var15) {
               System.out.println(var15);
            }
         }
      } else {
         var1 = true;
      }

      if (!var1) {
         ++aE;
      } else {
         int var2;
         if ((var2 = i(ax, 5)) == 18) {
            bc[30] = 1;
         }

         aF.a(aH);
         aD = false;
         int var3 = ao[g(var2, -1)];
         boolean var4;
         if (!(var4 = h.N[2] == 1)) {
            h.N[2] = 1;
         }

         aU = 100L;
         if (h.N[3] != 1) {
            aT = aB;
         }

         if (var4) {
            a(h.s[0], an, var3 + 6, false, false);
         }

         int var5 = an[var3 + 6 + 0] & 255;
         var3 += 7 + var5 * 2;
         var5 = an[var3] & 255;
         ++var3;
         int var6;
         if (var4) {
            label92:
            for(var6 = 0; var6 < var5; ++var6) {
               int var7 = an[var3 + 0] & 255 | an[var3 + 1] << 8;
               int var8 = an[var3 + 2] & 255;
               if (g.a(var7, (g)null, false)) {
                  var3 += 3;
                  int var9 = 0;

                  while(true) {
                     if (var9 >= var8) {
                        break label92;
                     }

                     int var10 = an[var3 + 0] & 255;
                     int var11 = an[var3 + 1] & 255;
                     boolean var12 = (var10 & 32) != 0;
                     int var13 = var10 & 192;
                     int var14 = var10 & 31;
                     if (var12) {
                        if (var13 == 128) {
                           g.g(var14, var11);
                        } else {
                           g.g(var14, bb[var14] + var11);
                        }
                     } else if (var13 == 128) {
                        g.f(var14, var11);
                     } else {
                        g.f(var14, bb[var14] + var11);
                     }

                     var3 += 2;
                     ++var9;
                  }
               }

               var3 += 3 + var8 * 2;
            }
         }

         var0.a(0);
         h.s[0].c[0] = h.ae[0];
         h.s[0].c[1] = h.ae[1];
         if (aW[0] == null) {
            g.p();
            h.k = true;
         } else {
            var6 = bo[70];
            if (bo[71] != var6) {
               h.s[0].a[35] = var6;
               h.z = 6;
            } else {
               h.z = 7;
            }
         }
      }
   }

   static void a(Graphics var0, int var1, int var2, int var3, int var4) {
      int var6 = h.s[0].a[var1];
      int[] var7 = new int[]{0, 0, 0};
      g.a(var1, var6, var7);
      int var8 = var7[2];

      for(int var5 = 0; var5 < 3; ++var5) {
         byte var9 = -1;
         if (var5 == 0) {
            var9 = 0;
         } else {
            int var10;
            int var11;
            if (var5 == 1) {
               var9 = 4;
               var10 = 1 + var2;
               var11 = 13 * (var6 - var7[0]) / (var7[1] - var7[0]);
               int var12 = var10 + var8 * 15 + var11;
               if (var7[1] <= var7[0]) {
                  return;
               }

               var0.setClip(var10, var3, var12 - var10, 400);
            } else if (var5 == 2) {
               if (var8 <= 2) {
                  var9 = 1;
               } else if (var8 <= 3) {
                  var9 = 2;
               } else {
                  var9 = 3;
               }

               var11 = (var10 = 1 + var2) + var8 * 15;
               var0.setClip(var10, var3, var11 - var10, 400);
            }
         }

         if (var9 >= 0) {
            h.u[2].a((Graphics)var0, var9, 0, var2, var3, 0, 0, 0);
         }
      }

      var0.setClip(var2, var3, 76 * (var6 - var7[0]) / (var7[1] - var7[0]), 400);
      h.u[2].a((Graphics)var0, var4, 0, var2, var3, 0, 0, 0);
   }

   static void c(Graphics var0) {
      int var2 = 0;
      g var4 = h.s[0];
      int var1;
      if (aZ == null) {
         aZ = new d[aW.length];

         for(var1 = 0; var1 < aZ.length; ++var1) {
            aZ[var1] = new d(h.u[1], 0, 0, (d)null);
         }
      }

      if (h.K == 0) {
         for(var1 = 13; var1 < 32; ++var1) {
            var4.a[var1] = bo[var1 * 2 + 0];
         }
      } else {
         int var6 = (h.K - 1) / 20;
         int var7 = h.K - 1 - var6 * 20;

         for(var1 = 0; var1 < aW.length; ++var1) {
            String var8;
            int var9;
            if ((var8 = aW[var1]) != null && (var9 = Integer.parseInt(var8.substring(var8.indexOf(35) + 1, var8.length()))) != 35) {
               var0.setClip(0, 0, 240, 400);
               int var10 = 130 + var2 * 29;
               g.a(var0, 82, var10, 156, 30, 12875393, 16777215, 0);
               byte var3;
               if (var8.charAt(0) == '+') {
                  aZ[var1].a(77);
                  var3 = 5;
               } else {
                  aZ[var1].a(78);
                  var3 = 6;
               }

               h.M.a(var0, var8.substring(1, var8.indexOf(35)), 86, var10 + 2, 20, 0);
               if (var2 == var6 && var7 < 20) {
                  int var11 = bo[var9 * 2 + 0];
                  int var12;
                  int var13 = (var12 = bo[var9 * 2 + 1]) - var11;
                  int var14 = (var7 << 8) / 20;
                  if (var13 != 0) {
                     var4.a[var9] = var11 + ((var12 - var11) * var14 >> 8);
                  }
               }

               a((Graphics)var0, var9, 86, var10 + 17, var3);
               var0.setClip(0, 0, 240, 400);
               var0.setColor(16777215);
               var0.fillRect(218, var10, 22, 29);
               aZ[var1].a = 55808;
               aZ[var1].b = var10 << 8;
               aZ[var1].a(var0);
               if (var2 == var6) {
                  aZ[var1].b();
               } else {
                  aZ[var1].g = 0;
                  aZ[var1].f = 0;
               }

               h.M.a(var0, Integer.toString(var4.a[var9]), 215, var10 + 7, 24, 0);
               ++var2;
            }
         }
      }

      if (h.K - 1 == var2 * 20 + 5) {
         for(var1 = 13; var1 < 32; ++var1) {
            var4.a[var1] = bo[var1 * 2 + 1];
         }

         var4.a[35] = bo[71];
         if (av != -1) {
            h.z = 15;
            return;
         }

         g.p();
         if (var4.b[17] > 0 && g.N.size() > 0 && ((String)g.N.elementAt(0)).charAt(0) == '!') {
            h.z = 8;
            return;
         }

         h.y = 0;
         h.k = true;
      }

   }

   static void n() {
      int[] var0 = new int[]{0, 0};
      al = -1;

      for(int var1 = 1; var1 < h.s.length; ++var1) {
         if (h.s[var1] != null && h.s[var1].b()) {
            h.s[var1].o();
            var0[0] = h.s[0].c[0] - h.s[var1].c[0];
            var0[0] = var0[0] * var0[0] >> 8;
            var0[1] = h.s[0].c[1] - h.s[var1].c[1];
            var0[1] = var0[1] * var0[1] >> 8;
            h.s[var1].t = var0[0] + var0[1];
            if (h.s[var1].y != null && (al == -1 || h.s[var1].t < h.s[al].t)) {
               al = (byte)var1;
            }
         }
      }

      ba = false;
   }

   static void o() {
      if (g.l()) {
         for(int var0 = 1; var0 < h.s.length; ++var0) {
            if (h.s[var0] != null && h.s[var0].b()) {
               h.n[h.s[var0].b[5]].b();
               h.s[var0].g();
            }
         }

      }
   }

   static void p() {
      try {
         GloftMMN.b("/9");
         GloftMMN.h(0);
         as = GloftMMN.g();
         at = new byte[as * 11];
         GloftMMN.a(at);
         I = GloftMMN.j(1);
         boolean var0 = false;
         an = GloftMMN.j(2);
         am = an[0] & 255;
         ao = new int[am];
         ap = new long[am];
         int var3 = 1;

         for(int var7 = 0; var7 < am; ++var7) {
            ao[var7] = var3;
            int var4 = an[var3 + 6 + 0] & 255;
            var3 += 7 + var4 * 2;
            var4 = an[var3] & 255;
            ++var3;

            for(int var1 = 0; var1 < var4; ++var1) {
               int var5 = an[var3 + 2] & 255;
               var3 += 3 + var5 * 2;
            }
         }

         GloftMMN.f();
      } catch (Exception var6) {
      }
   }
}
