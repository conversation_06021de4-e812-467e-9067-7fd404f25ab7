import java.util.Vector;
import javax.microedition.lcdui.Graphics;
import javax.microedition.lcdui.Image;

public final class g {
   int[] a;
   byte[] b;
   int[] c;
   int[] d;
   byte[] e;
   static byte[] f = null;
   static byte[] g = new byte[]{4, 0, 1, 2, 5, 6, 9, 11, 3, 28, 30, 31, 12, 7, 10, 14, 19, 16, 17, 8, 15, 13, 18, 29, 20, 22, 23, 24, 27, 26, 25, 21, 32, 33, 34, 35, 36, 37, 38};
   static byte[] h;
   static int[] i;
   static byte[] j;
   byte[] k;
   byte[] l;
   byte[] m;
   int[] n;
   int[] o;
   static long p;
   static long[] q;
   byte[] r;
   boolean s;
   int t;
   static int u;
   static int v;
   static int w;
   static g x;
   d y;
   d z;
   boolean A;
   static byte[] B;
   static int C;
   static int D;
   static byte[] E;
   static int[] F;
   static int[] G;
   static int H;
   static int[] I;
   static int J;
   static byte K;
   static int L;
   static String M;
   static Vector N;
   static int O;
   static byte P;
   static int Q;
   static long R;
   static d S;
   static d T;
   static Image[] U;
   static byte V;
   static byte W;
   static byte[] X;
   static byte[] Y;
   static Image[] Z;
   static byte aa;
   static String ab;
   static String ac;
   static String ad;
   static int ae;
   static long af;
   static long[] ag;
   static byte[] ah;
   static boolean[] ai;
   static short[][] aj;
   static String ak;
   static byte al;
   static byte[] am;
   byte[] an;
   static byte ao;
   static byte[] ap;
   static short[] aq;
   static byte ar;
   static byte[] as;
   static byte[] at;
   static boolean au;
   int[] av;

   final void a() {
      this.e = new byte[3];
      this.e[0] = f.K;
      this.e[1] = this.b[14];
   }

   final boolean b() {
      return this.b[20] == 1 && (this.e == null || this.a[43] != 127 || this.e[0] == f.K || this.b[19] == 1);
   }

   final void a(int var1, int var2) {
      int var3 = this.c[0] >> 8;
      int var4 = this.c[1] >> 8;
      int var5 = var3 + var4 * f.t;
      short[] var10000 = f.k;
      var10000[var5] &= -15873;
      this.c[0] = var1;
      this.c[1] = var2;
      if (var1 != -255) {
         var3 = this.c[0] >> 8;
         var4 = this.c[1] >> 8;
         var5 = var3 + var4 * f.t;
         var10000 = f.k;
         var10000[var5] &= -15873;
         var10000 = f.k;
         var10000[var5] = (short)(var10000[var5] | (this.b[6] & 255) << 9);
      }
   }

   final void a(Graphics var1, boolean var2) {
      if (h.h != 18) {
         int[] var3 = new int[]{0, 0};
         d var4 = h.n[this.b[5]];
         f.a(this.c[0], this.c[1], var3);
         int var5 = 1 + (this.b[2] & 255) * 5;
         int var6;
         if ((var6 = f.aL[var5 + 4] & 255) != 255) {
            if (this.y == null) {
               this.y = new d(h.u[1], 0, -63, var4);
            }

            if (this.z == null) {
               this.z = new d(h.u[1], 0, 0, (d)null);
            }

            this.y.a(var6);
            this.z.a(var6);
         } else {
            this.y = null;
         }

         var4.a = var3[0] + 0 << 8;
         var4.b = var3[1] + 75 << 8;
         if (var4.e == 67) {
            if (var4.c != 0) {
               var4.b += 1536;
               var4.a -= 2560;
            } else {
               var4.b += 1536;
               var4.a += 1536;
            }
         }

         if (!var2) {
            this.d = var4.c();
            int[] var10000 = this.d;
            var10000[0] >>= 8;
            var10000 = this.d;
            var10000[1] >>= 8;
            var10000 = this.d;
            var10000[2] >>= 8;
            var10000 = this.d;
            var10000[3] >>= 8;
            this.s = false;
            if (f.a(this.d[0], this.d[1], this.d[2], this.d[3], 0, 75, 240, 315)) {
               this.s = true;
               var4.d.m = this.b[15];
               var4.a(var1);
               f.a(var1, this);
               if (this.y != null && h.y == 0 && f.al == this.b[6]) {
                  var1.setClip(0, 75, 240, 240);
                  if (this.y.e < 63) {
                     f.a(var4, var4.c == 0);
                     d var7;
                     (var7 = h.v[18]).a(30);
                     var7.f = 1;
                     var7.d.a((Graphics)var1, 30, 1, var7.a >> 8, var7.b >> 8, var7.c, 0, 0);
                     int[] var12 = var7.c();
                     this.z.a = var12[0];
                     this.z.b = var12[1];
                     this.z.a(var1);
                     this.z.b();
                     var7.e = -1;
                     return;
                  }

                  this.y.a(var1);
                  this.y.b();
               }
            }

         } else if (this.s) {
            var1.setClip(0, 75, 240, 240);
            if (this == x) {
               d var8;
               int var9;
               if ((var9 = (var8 = h.v[18]).e) == -1) {
                  var8.a(29);
               } else if (var8.a() && var9 == 29) {
                  var8.a(30);
                  var9 = 255;
               } else {
                  var9 = 255;
               }

               var8.a(var1);
               if (!var8.a()) {
                  var8.b();
               }

               if (var9 == 255) {
                  int[] var10 = var8.c();
                  d var11;
                  (var11 = h.v[17]).a = var10[0];
                  var11.b = var10[1];
                  var11.a(var1);
                  if (!var11.a()) {
                     var11.b();
                     return;
                  }
               }
            } else {
               if (h.y == 0 && f.av == this.b[6]) {
                  f.a(var1, var4, true, false, true, this);
                  return;
               }

               f.a(var1, var4, false, false, false, this);
            }

         }
      }
   }

   private void b(int var1, boolean var2) {
      int var4 = f.d(var1, 0);
      this.a[0] = f.aK[var4 + 1];
      int var6 = f.aK[0] & 255;
      int var7 = f.aK[1 + var6 * 2] & 255;
      var4 = f.d(var1, 1);

      int var3;
      for(var3 = 1; var3 < var7; ++var3) {
         int var8 = f.aK[1 + var6 * 2 + 1 + var3] & 255;
         boolean var11 = true;
         if (!var2 && (var8 >= 4 && var8 <= 11 || var8 > 35)) {
            var11 = false;
         }

         if (var8 == 41) {
            var11 = true;
         }

         int var9;
         int var10;
         int var12;
         if ((var10 = (var9 = f.aK[var4++] & 255) & 3) == 0) {
            if (var11) {
               this.a[var8] = f.aK[var4];
            }

            ++var4;
         } else if (var10 == 1) {
            if (var11) {
               this.a[var8] = GloftMMN.c(f.aK[var4], f.aK[var4 + 1] + 1);
            }

            var4 += 2;
         } else if (var10 == 2) {
            var9 = (var9 & 252) >> 2;

            for(var12 = GloftMMN.c(0, var9); var9-- > 0; --var12) {
               if (var12 == 0 && var11) {
                  this.a[var8] = f.aK[var4];
               }

               ++var4;
            }
         }

         if (var8 >= 13 && var8 <= 33 && var8 <= 26 || var8 == 41) {
            this.a[var8] = this.a[var8] * (f.bc[17] + 100) / 100;
            var12 = d(var8, 3);
            if (this.a[var8] >= var12) {
               this.a[var8] = var12 - 1;
            }
         }
      }

      C = f.d(var1, -1);
      this.k = a((byte[])null);
      this.l = a((byte[])null);
      this.m = a(this.l);
      if ((var3 = f.d(var1)) >= 0) {
         this.a[1] = 474 + var3;
      } else if (this.a[0] == 0) {
         this.a[1] = GloftMMN.c(402, 417);
      } else {
         this.a[1] = GloftMMN.c(417, 429);
      }

      if (!var2) {
         this.a[39] = 0;
         this.a[42] = 0;

         for(var3 = 0; var3 < this.r.length; ++var3) {
            this.r[var3] = 0;
         }
      }

   }

   g(int var1, int var2, int var3, boolean var4, boolean var5) {
      try {
         this.d = new int[4];
         this.r = new byte[112];
         this.b = new byte[21];
         this.b[7] = -1;
         this.a = new int[D];
         this.n = new int[2];
         this.o = new int[2];
         if (!var4) {
            this.b[4] = -1;
            this.b[6] = (byte)var2;
            this.b[18] = -1;
            this.b[20] = 1;
         }

         int var6;
         if (var2 == 0) {
            q = new long[D];

            for(var6 = 0; var6 < D; ++var6) {
               q[var6] = -1L;
            }

            for(var6 = 0; var6 < D; ++var6) {
               this.a[var6] = d(var6, 4);
               if (var6 == 35) {
                  int[] var10000 = this.a;
                  var10000[var6] *= 5;
               }
            }

            q[28] = 75L;
            q[25] = 60L;
            q[24] = 180L;
            q[22] = 70L;
            q[29] = 115L;
            q[26] = 120L;
            this.r();
         } else if (!var4) {
            this.b[13] = (byte)var3;
            this.b(var3, true);
            var1 = 7 + this.a[0] * 2;
         }

         if (!var4) {
            this.b[0] = (byte)var1;
         }

         this.c = new int[2];
         this.c[0] = 0;
         this.c[1] = 0;
         int var7 = (var1 - 7) / 2;
         if (!var4) {
            if (var5) {
               this.a(var1, var2, true);
            }

            for(var6 = 0; var6 < h.n.length; ++var6) {
               if (h.n[var6] == null) {
                  this.b[5] = (byte)var6;
                  h.n[var6] = new d(h.o[var7], 0, 0, (d)null);
                  if (var6 == 0) {
                     h.t[0] = new d(h.o[var7], 0, 0, (d)null);
                  }
                  break;
               }
            }
         }

      } catch (Exception var8) {
      }
   }

   final void c() {
      this.a = null;
      h.n[this.b[5]] = null;
      this.b = null;
      this.c = null;
      this.n = null;
      this.o = null;
      System.gc();
   }

   static void a(int var0, int var1, byte var2) {
      int var4 = h.s[0].a[0] * 126;
      if (var0 >= 0) {
         h.e[var4 + var0] = var2;
      } else {
         if (var1 >= 0) {
            h.e[var4 + 63 + var1] = var2;
         }

      }
   }

   static int b(int var0, int var1) {
      int var3 = h.s[0].a[0] * 126;
      return var0 != -1 ? h.e[var3 + var0] : h.e[var3 + 63 + var1];
   }

   final void a(int var1, int var2, boolean var3) {
      boolean var4 = h.H == 30 && f.bc[2] >= 8 && f.bc[2] <= 19;
      g var8 = this;
      int var9 = (var1 - 7) / 2;
      a var10 = h.o[var9];
      byte var11 = j[var9];
      if (var3) {
         this.b[15] = (byte)var11;
      }

      if (var10.w == null) {
         var10.w = new Image[var10.l][];
      }

      int[] var12 = new int[6];
      int[] var13 = new int[6];

      int var5;
      for(var5 = 0; var5 < 6; ++var5) {
         var12[var5] = -1;
      }

      a var7;
      byte var16;
      int var10002;
      boolean var20;
      if (var2 == 0) {
         var12[4] = this.a[8] + 1;
         var13[4] = this.a[9] & 255;
         var12[5] = this.a[4] + 1;
         var13[5] = this.a[5] & 255;
         var12[3] = 1;
         var13[3] = ((this.a[9] & 255) + 40) % h.p[var9][4].l;
         byte var15;
         if (this.a[6] != -1) {
            byte var14 = h.a[var9][this.a[6] * 3 + 0];
            var15 = h.a[var9][this.a[6] * 3 + 1];
            var16 = h.a[var9][this.a[6] * 3 + 2];
            this.a[43] = var14;
            var12[2] = var14 + 1;
            var13[2] = this.a[7] & 255;
            this.a[44] = var15;
            var12[0] = var15 + 1;
            var13[0] = (this.a[7] & 255) + var16;
            var13[0] %= h.p[var9][0].l;
         }

         if (this.a[6] == -1 || h.H == 36) {
            var12[2] = 0;
            var12[0] = 0;
            var12[4] = 0;
         }

         var12[1] = var12[0];
         var13[1] = var13[0] & 255;
         a var32 = h.p[0][5];
         var15 = h.r[0][5];
         var5 = (var32.a / var15 - 1) * var15;
         Z = new Image[var15];

         for(int var38 = var5; var38 < var5 + var15; ++var38) {
            var32.a(var13[5] & 255, var38, var38, -1, var32.l - 2);
            Z[var38 - var5] = var32.w[var32.l - 2][var38];
         }
      } else {
         if (!var3) {
            int[] var37 = new int[6];

            int var35;
            for(var35 = 0; var35 < 6; ++var35) {
               var37[var35] = -1;
            }

            var16 = this.b[15];
            int[] var40 = new int[6];

            for(var35 = 0; var35 < var10.a; ++var35) {
               byte var41;
               if ((var41 = h.q[var9][var35]) > 0 && var41 != 0 && var41 != 1 && var41 != 3 && var41 != 5) {
                  while(true) {
                     while(var37[var41] == -1) {
                        for(var5 = 1; var5 < h.s.length; ++var5) {
                           if ((var8 = h.s[var5]) != null && var8.a[0] == this.a[0] && (var8.b[20] != 0 || var5 == 0) && GloftMMN.c(0, 100) <= 10) {
                              var37[var41] = var5;
                              break;
                           }
                        }
                     }

                     int var42 = -1;
                     var20 = true;
                     int var43;
                     if (var41 == 2) {
                        var42 = var8.a[6];
                     } else if (var41 == 5) {
                        var42 = var8.a[4];
                        var43 = h.p[var9][5].a / h.r[var9][5] - 2;
                        if (var42 >= var43) {
                           var20 = false;
                        }
                     } else if (var41 == 4) {
                        var42 = var8.a[8];
                     }

                     if (var20) {
                        var7 = h.p[var9][var41];
                        var43 = var40[var41] + var42 * h.r[var9][var41];
                        var10.w[var16][var35] = var7.w[0][var43];
                     }

                     var10002 = var40[var41]++;
                     break;
                  }
               }
            }

            return;
         }

         g var33 = h.s[0];
         var12[2] = this.a[6] + 1;
         var13[2] = this.a[7] & 255;
         var12[4] = this.a[8] + 1;
         var13[4] = this.a[9] & 255;
         var12[5] = this.a[4] + 1;
         var13[5] = this.a[5] & 255;
         if (var4) {
            var12[2] = 0;
            var13[2] = 0;
            var12[4] = 0;
            var13[4] = 0;
         }

         if (this.a[0] == 0) {
            var12[3] = 1;
         } else {
            var12[3] = var33.a[10] + 1;
         }

         var13[3] = var33.a[11] & 255;
         var12[0] = 0;
         var13[0] = 0;
         var12[1] = var12[0];
         var13[1] = var13[0];
      }

      int[] var34 = new int[6];
      a var36 = h.o[0];
      Object var39 = null;
      boolean var17 = false;
      boolean var18 = false;
      boolean var19 = false;
      var20 = false;
      if (var10.w[var11] == null) {
         var10.w[var11] = new Image[var10.a];
      }

      for(var5 = 0; var5 < var10.a; ++var5) {
         byte var21;
         if ((var21 = h.q[var9][var5]) == -1) {
            int var44;
            if (var9 == 0) {
               var44 = var5;
            } else {
               var44 = var5 - 69 + 64;
            }

            if (var44 >= 64 && var44 < 113) {
               if (var36.w == null || var36.w[0] == null || var36.w[0][var44] == null) {
                  var36.a(0, var44, var44, -1, 0);
               }

               var10.w[var11][var5] = var36.w[0][var44];
            } else {
               boolean var45 = true;
               if (var4) {
                  if (var44 >= 113) {
                     var45 = true;
                  }
               } else if (var44 >= 113) {
                  var45 = false;
               }

               if (var45) {
                  if (var10.w == null || var10.w[0] == null || var10.w[0][var5] == null) {
                     var10.a(0, var5, var5, -1, 0);
                  }

                  var10.w[var11][var5] = var10.w[0][var5];
               }
            }
         } else {
            int var23;
            if (var12[var21] == 0) {
               var23 = var10.l - 1;
               if (var10.w == null || var10.w[var23] == null || var10.w[var23][var5] == null) {
                  var10.a(0, var5, var5, -1, var23);
               }

               if (var10.w[var11] == null) {
                  var10.w[var11] = new Image[var10.a];
               }

               var10.w[var11][var5] = var10.w[var23][var5];
            } else {
               var7 = h.p[var9][var21];
               var23 = var34[var21];
               Object var24 = null;
               int var25 = var9;
               int var26 = var12[var21];
               boolean var27 = false;
               if (var21 == 5) {
                  int var29 = var12[5] - 1;
                  int var30 = h.p[var9][5].a / h.r[var9][5];
                  if (this.a[0] == 0) {
                     --var30;
                  }

                  var8.b[1] = (byte)var30;
                  byte[] var10000;
                  boolean var31;
                  if (var31 = this.a[0] == 0) {
                     var10000 = var8.b;
                     var10000[1] = (byte)(var10000[1] + X.length);
                  } else {
                     var10000 = var8.b;
                     var10000[1] = (byte)(var10000[1] + Y.length);
                  }

                  int var28;
                  if (var31) {
                     var28 = var30 - 1;
                  } else {
                     var28 = var8.b[1] - 1;
                  }

                  if (var29 == var8.b[1] - 1) {
                     var27 = true;
                     if (!var31) {
                        var25 = 0;
                        var29 = Y[var29 - var30];
                     } else {
                        var29 = var28;
                     }
                  } else if (var29 >= var28 && this.a[0] == 0) {
                     ++var29;
                  }

                  if (!var27 && var29 >= var30) {
                     var29 -= var30;
                     if (var31) {
                        var25 = 1;
                        var29 = X[var29];
                     } else {
                        var25 = 0;
                        var29 = Y[var29];
                     }
                  }

                  var7 = h.p[var25][var21];
                  var12[var21] = var29 + 1;
               }

               var23 += (var12[var21] - 1) * h.r[var25][var21];
               if (h.h == 17 || h.h == 18 || var7.w == null || var7.w[0] == null || var7.w[0][var23] == null) {
                  var7.a(var13[var21], var23, var23, -1, 0);
               }

               if (var10.w[var11] == null) {
                  var10.w[var11] = new Image[var10.a];
               }

               var10.w[var11][var5] = var7.w[0][var23];
               var10002 = var34[var21]++;
               var12[var21] = var26;
            }
         }
      }

      ++j[var9];
   }

   static void a(boolean var0) {
      if (!var0) {
         h.p = new a[2][];
      }

      int var1;
      int var2;
      for(var1 = 0; var1 < 2; ++var1) {
         if (!var0) {
            if (var1 == 0) {
               GloftMMN.b("/5");
            } else {
               GloftMMN.b("/6");
            }

            h.p[var1] = new a[6];
         }

         for(var2 = 0; var2 < 6; ++var2) {
            if (!var0) {
               h.a(0 + var2, h.p[var1], var2, false, false, 1, false);
               a var4;
               (var4 = h.p[var1][var2]).h = null;
               var4.i = null;
               var4.d = null;
               var4.e = null;
               var4.f = null;
               var4.i = null;
               var4.j = null;
               var4.g = null;
               System.gc();
            } else {
               h.p[var1][var2].b();
               h.p[var1][var2].c();
            }
         }

         if (!var0) {
            GloftMMN.f();
         }
      }

      if (!var0) {
         GloftMMN.b("/7");
         a var3;
         if ((var3 = h.p[0][0]).l == 1) {
            GloftMMN.h(0);
            int var6 = GloftMMN.g();
            var3.l = var6;
            var3.k = new int[var6][];
            byte[] var5 = new byte[16];
            var1 = 0;

            label96:
            while(true) {
               if (var1 >= var6) {
                  for(var1 = 0; var1 < 2; ++var1) {
                     for(var2 = 0; var2 < 6; ++var2) {
                        if (var2 != 5) {
                           h.p[var1][var2].k = h.p[0][0].k;
                           h.p[var1][var2].l = var6;
                        }
                     }
                  }

                  GloftMMN.h(1);
                  var6 = GloftMMN.g();
                  (var3 = h.p[0][5]).l = var6;
                  var3.k = new int[var6][];

                  for(var1 = 0; var1 < var6; ++var1) {
                     GloftMMN.a(var5, 0, 16);
                     var3.k[var1] = new int[8];

                     for(var2 = 0; var2 < 8; ++var2) {
                        var3.k[var1][var2] = f.a(var5[var2 * 2 + 0] & 255 | (var5[var2 * 2 + 1] & 255) << 8);
                     }
                  }

                  var1 = 0;

                  while(true) {
                     if (var1 >= 2) {
                        break label96;
                     }

                     h.p[var1][5].k = h.p[0][5].k;
                     h.p[var1][5].l = var6;
                     ++var1;
                  }
               }

               GloftMMN.a(var5, 0, 16);
               var3.k[var1] = new int[8];

               for(var2 = 0; var2 < 8; ++var2) {
                  var3.k[var1][var2] = f.a(var5[var2 * 2 + 0] & 255 | (var5[var2 * 2 + 1] & 255) << 8);
               }

               ++var1;
            }
         }

         GloftMMN.f();
      }

   }

   final void c(int var1, int var2) {
      int[] var10000;
      int var3;
      if (var1 == 35) {
         H = var3 = this.a[35];
         var10000 = this.a;
         var10000[35] += var2;
         if (this.a[35] < 0) {
            this.a[35] = 0;
         }

         if (this.a[35] > 1000000000) {
            this.a[35] = 1000000000;
         }

         this.b(35, var3, this.a[35]);
      } else {
         var3 = d(var1, 2);
         int var4 = d(var1, 3);
         int var5 = this.a[var1];
         var10000 = this.a;
         var10000[var1] += var2;
         if (this.a[var1] < var3) {
            this.a[var1] = var3;
         }

         if (this.a[var1] > var4) {
            this.a[var1] = var4;
         }

         if (var1 == 39 || this.a[var1] != var5) {
            this.b(var1, var5, this.a[var1]);
         }

      }
   }

   static int d(int var0, int var1) {
      int var2 = F[var0];
      if (var1 > 0) {
         if (var1 == 1) {
            var2 += 2;
         } else if (var1 == 2) {
            var2 += 3;
         } else if (var1 == 3) {
            var2 += 4;
         } else if (var1 == 4) {
            var2 += 5;
         } else if (var1 == 5) {
            var2 += 6;
         }

         if (var1 != 2 && var1 != 3) {
            return E[var2] & 255;
         } else {
            return var0 != 36 && var0 != 37 && var0 != 38 ? E[var2] : E[var2] & 255;
         }
      } else {
         return E[var2 + 0] & 255 | (E[var2 + 1] & 255) << 8;
      }
   }

   static int e(int var0, int var1) {
      int var2 = d(var0, 5);

      for(int var3 = 0; var3 < var2; ++var3) {
         int var4 = F[var0] + 7 + var3 * 4;
         byte var5 = E[var4 + 2];
         byte var6 = E[var4 + 3];
         int var7 = E[var4 + 0] & 255 | (E[var4 + 1] & 255) << 8;
         if (var1 >= var5 && var1 <= var6) {
            return var7;
         }
      }

      return -1;
   }

   static void a(int var0, int var1, int[] var2) {
      int var3 = d(var0, 5);

      for(int var4 = 0; var4 < var3; ++var4) {
         int var5 = F[var0] + 7 + var4 * 4;
         byte var6 = E[var5 + 2];
         byte var7 = E[var5 + 3];
         if (var1 >= var6 && var1 <= var7) {
            var2[0] = E[var5 + 2];
            var2[1] = E[var5 + 3];
            var2[2] = var4;
            return;
         }
      }

   }

   static void d() {
      GloftMMN.b("/4");
      E = GloftMMN.j(0);
      D = E[0];
      F = new int[D];
      int var1 = 1;

      for(int var0 = 0; var0 < D; ++var0) {
         F[var0] = var1;
         var1 += E[var1 + 6] * 4;
         var1 += 7;
      }

   }

   private static void i(int var0, int var1) {
      a(GloftMMN.k(var0), var1);
   }

   static void a(String var0, int var1) {
      ad = var0;
      if (ad.charAt(0) == '~') {
         ad = ad.substring(1, ad.length());
      }

      ae = var1 & 255 | 256;
      af = f.aT;
   }

   static void a(int var0) {
      if (var0 == 884) {
         if (f.bb[11] != 0) {
            return;
         }

         f.bb[11] = 1;
      }

      a(GloftMMN.k(var0));
   }

   private void q() {
      int var2 = 0;
      int[] var3 = new int[]{13, 19, 30};

      for(int var1 = 1; var1 < 3; ++var1) {
         if (this.a[var3[var1]] > this.a[var3[var2]]) {
            var2 = var1;
         }
      }

      if ((var2 = var3[var2]) == 13) {
         ab = GloftMMN.k(843);
      } else if (var2 == 19) {
         ab = GloftMMN.k(844);
      } else {
         ab = GloftMMN.k(845);
      }

      ac = GloftMMN.k(d(this.k[0] & 255, 0));
   }

   static void b(boolean var0) {
      long var1 = f.aT;
      if (!var0) {
         byte var4;
         if (((var4 = f.bc[2]) < 22 || var4 > 23) && (var4 < 0 || var4 >= 8)) {
            ah[29] = 1;
            ah[9] = 1;
            ah[32] = 1;
            ah[33] = 1;
         } else {
            ah[29] = 0;
            ah[9] = 0;
            ah[32] = 0;
            ah[33] = 0;
         }

         if (var4 >= 2 && var4 < 11) {
            ah[21] = 0;
         } else {
            ah[21] = 1;
         }

         if (h.y == 0 && h.z == -1) {
            for(int var5 = 0; var5 < 5; ++var5) {
               long var6;
               if ((var6 = ag[var5]) > 0L && var1 >= var6) {
                  boolean var8 = true;
                  switch(var5) {
                  case 0:
                     if ((h.H < 0 || h.H > 2) && h.H != 5) {
                        if (f.bc[6] != 3 && f.bc[0] < 40) {
                           a((int)498);
                           a((int)499);
                           f(6, 1);
                           ah[0] = 0;
                           ah[1] = 0;
                           var8 = false;
                           ag[0] = f.aT + 4320000L;
                        }
                     } else {
                        var8 = false;
                     }
                     break;
                  case 1:
                     f(1, 1);
                     a((int)847);
                     a((int)500);
                     a((int)501);
                     a((int)502);
                     a((int)503);
                     a((int)504);
                     a((int)935);
                     a((int)936);
                  case 2:
                  default:
                     break;
                  case 3:
                     f(4, 12);
                     a((int)652);
                     aa = 5;
                     if (h.s[0].a[0] == 1) {
                        a(GloftMMN.k(647) + " " + GloftMMN.k(648) + " " + GloftMMN.k(649), (int)2);
                     } else {
                        a(GloftMMN.k(647) + " " + GloftMMN.k(648) + " " + GloftMMN.k(649), (int)13);
                     }

                     f.bc[31] = 0;
                     h.m = 0;
                     break;
                  case 4:
                     a((int)846);
                     f(13, 0);
                     h.s[0].c(24, -h.s[0].a[24]);
                     h.m = 26;
                     f.bc[31] = 0;
                  }

                  if (var8) {
                     ag[var5] = 0L;
                  }
               }
            }

         }
      }
   }

   static void e() {
      int var0;
      for(var0 = 0; var0 < 39; ++var0) {
         ah[var0] = 0;
         ai[var0] = true;
      }

      ah[1] = 1;
      ag[1] = f.aT + 333L;
      f.aT = 540000L;
      f.aU = 100L;
      f.aV = -1;
      if (f.bo != null) {
         for(var0 = 0; var0 < f.bo.length; ++var0) {
            f.bo[var0] = 0;
         }
      }

      h.s[0].a[35] = 50;
      aa = -1;
   }

   final void f() {
      for(int var1 = 0; var1 < D; ++var1) {
         if (q[var1] == 0L) {
            switch(var1) {
            case 22:
               this.c(22, -1);
               q[22] = 70L;
            case 23:
            case 27:
            default:
               break;
            case 24:
               this.c(24, -1);
               q[24] = 180L;
               break;
            case 25:
               this.c(25, 1);
               q[25] = 60L;
               break;
            case 26:
               this.c(26, -10);
               q[26] = 120L;
               break;
            case 28:
               this.c(28, 2);
               q[28] = 75L;
               break;
            case 29:
               if (h.n[0].e != 56) {
                  this.c(29, 6);
               }

               q[29] = 115L;
            }
         }
      }

   }

   private void b(int var1, int var2, int var3) {
      g var4 = h.s[0];

      int var6;
      label467:
      for(var6 = 0; var6 < aj.length; ++var6) {
         if (aj[var6].length == 1 && aj[var6][0] == var1) {
            ++var6;

            while(true) {
               if (var6 >= aj.length || aj[var6].length == 1) {
                  break label467;
               }

               if (var3 >= aj[var6][0] && var2 < aj[var6][1]) {
                  a((int)aj[var6][2]);
                  if (aj[var6].length == 4) {
                     a((int)aj[var6][3]);
                  }
               }

               ++var6;
            }
         }
      }

      if (var6 == aj.length) {
         boolean var7 = false;
         if (var1 == 22) {
            if (var3 < 36 && var2 > 37) {
               a((int)675);
               var4.c(17, -2);
            } else if (var3 < 40) {
               var4.c(16, -2);
            }
         }

         if (var1 == 24) {
            if (var3 >= 50 && var2 < 50) {
               var4.a((byte)13, (byte)0, (int)799);
            } else if (var3 >= 80 && var2 < 80) {
               a((int)355);
            } else if (var3 < 50 && var2 >= 50) {
               var4.a((byte)13);
            }
         }

         if (var1 == 25) {
            if (var3 >= 50 && var2 < 50) {
               var4.a((byte)22, (byte)0, (int)800);
            } else if (var3 < 50) {
               var4.a((byte)22);
            } else if (var3 >= 85 && var2 < 85) {
               a((int)562);
            }
         }

         if (var1 == 28) {
            if (var3 >= 35 && var2 < 35) {
               var4.a((byte)34, (byte)0, (int)798);
            } else if (var3 < 35) {
               var4.a((byte)34);
            }

            if (var3 >= 55 && var2 < 55) {
               a((int)758);
               var4.c(30, -3);
            }

            if (var3 >= 70 && var2 < 70) {
               a((int)569);
            } else if (var3 >= 85 && var2 < 85) {
               a((int)570);
            }
         }

         if (var1 == 29) {
            boolean var8 = h.n[0].e != 56;
            if (var3 >= 55 && var2 < 55 && var8) {
               var4.a((byte)20, (byte)0, (int)801);
               var4.c(16, -1);
               a((int)676);
            } else if (var3 >= 55 && var3 >= var2 && var8) {
               var4.c(16, -1);
               a((int)676);
            } else if (var3 < 50) {
               var4.a((byte)20);
            } else if (var3 >= 75 && var2 < 75 && var8) {
               a((int)571);
            }
         }

         if (var1 == 35 && var3 >= 100 && var2 < 100 && f.bc[0] < 6) {
            f(0, 6);
            f(6, 1);
            if (h.s[0].a[0] == 1) {
               i(612, 2);
            } else {
               i(612, 13);
            }

            aa = 0;
            f(3, 3);
         }

         if (var1 == 36) {
            if (var3 == 1 && var2 == 0) {
               a((int)677);
            }

            if (var3 > var2) {
               if (f.bc[8] == 1 && h.H == 14) {
                  g(6, f.bb[6] + 1);
                  if (f.bb[6] == 1) {
                     f(8, 4);
                     a((int)658);
                     if (h.s[0].a[0] == 1) {
                        a(GloftMMN.k(659), (int)2);
                     } else {
                        a(GloftMMN.k(659), (int)13);
                     }

                     au = true;
                     f(0, 24);
                  } else {
                     a((int)662);
                     au = true;
                  }
               }

               if (f.av > 0 && f.d(h.s[f.av].b[13]) == -1) {
                  a((int)884);
               }
            }

            if (var3 == 10 && f.bc[17] < 10) {
               a((int)772);
               f(17, 10);
            } else if (var3 == 20 && f.bc[17] < 20) {
               a((int)772);
               f(17, 20);
            } else if (var3 == 30 && f.bc[17] < 30) {
               a((int)772);
               f(17, 30);
            } else if (var3 == 35 && f.bc[17] < 35) {
               a((int)772);
               f(17, 40);
            } else if (var3 == 40 && f.bc[17] < 40) {
               a((int)772);
               f(17, 50);
            }
         }

         if (var1 == 37 && f.av > 0 && f.d(h.s[f.av].b[13]) == -1) {
            a((int)884);
         }

         int var19;
         if (var1 == 39) {
            var19 = f.d(this.b[13]);
            byte var20 = h.E[h.D - 2];
            byte var9 = h.E[h.D - 1];
            int var10 = f.c(var20, -1);
            byte var11 = f.aJ[var10 + 0];
            var10 = f.c(var20, var9);
            byte var12 = f.aJ[var10 + 0];
            this.g();
            if (var3 < var2 && this.a[40] < this.a[41] && this.a[42] > 0 && var19 == -1) {
               a((int)760);
               var4.c(38, 1);
               this.a[42] = 0;
               return;
            }

            if ((var11 == 65 || var11 == 66) && this.a[42] == 0 && this.a[40] >= this.a[41] && !a(11, this, false)) {
               a((int)637);
            }

            if (var11 == 67 && (var12 == 78 || var12 == 77) && this.a[42] == 0 && this.a[40] >= this.a[41] && var3 >= var2) {
               this.c(42, this.a[40]);
            }

            int var13 = e(39, var2);
            int var14;
            if ((var14 = e(39, var3)) > var13) {
               if (var14 > var13 && var13 < 287 && var14 > 286) {
                  var4.c(36, 1);
               }

               if (var14 == 285) {
                  if (!c(this)) {
                     if (var19 != -1) {
                        b(this);
                     }

                     if (var19 == 3 || var19 == 14) {
                        f(4, 1);
                     }
                  }
               } else if (var14 == 286) {
                  if (var19 == 0) {
                     a((int)497);
                     a((int)638);
                     f(5, 1);
                     ah[17] = 1;
                     h.m = 17;
                  } else if (var19 == 4 && f.bc[3] < 2) {
                     f(3, 2);
                     a((int)626);
                     a((int)627);
                     au = true;
                     int var18 = var4.a[35];
                     var4.c(35, 100);
                     f.bo[70] = var18;
                     f.bo[71] = var18 + 100;
                  } else if (f.bc[4] < 2 && (var19 == 3 || var19 == 14 || var19 == 9 || var19 == 15 || var19 == 10 || var19 == 16)) {
                     a((int)629);
                     a((int)639);
                     f(4, 2);
                     a((int)776);
                     if (h.s[0].a[0] == 1) {
                        a(GloftMMN.k(663), (int)2);
                     } else {
                        a(GloftMMN.k(663), (int)13);
                     }

                     aa = 0;
                     h.m = 1;
                  } else if (var19 == 8) {
                     g(4, 1);
                     if (f.bc[0] == 24) {
                        f(0, 25);
                     }

                     ah[19] = 1;
                     a((int)664);
                     au = true;
                  } else if (var19 == 11) {
                     f(0, 26);
                     a((int)679);
                     if (h.s[0].a[0] == 1) {
                        a(GloftMMN.k(680), (int)2);
                     } else {
                        a(GloftMMN.k(680), (int)13);
                     }

                     aa = 4;
                     au = true;
                  } else if (var19 == -1 && f.bc[16] == 0) {
                     f(16, 1);
                     a((int)761);
                     a((int)762);
                  }
               } else if (var14 == 287) {
                  if (var19 == 12) {
                     f(0, 40);
                     a((int)681);
                     a((int)682);
                     au = true;
                     if (h.s[0].a[0] == 1) {
                        a(GloftMMN.k(683), (int)2);
                     } else {
                        a(GloftMMN.k(683), (int)13);
                     }

                     aa = 2;
                     ah[20] = 1;
                     f[20] = 1;
                  } else {
                     if (var19 == 22) {
                        f(0, 46);
                        this.a((int)87, (int)(h.n[this.b[5]].c != 0 ? 1 : 0), (int)-1);
                        a((int)684);
                        a(GloftMMN.k(684), (int)22);
                        au = true;
                        return;
                     }

                     if (var19 == 19) {
                        f(0, 42);
                     }
                  }
               } else if (var14 == 288) {
                  a(GloftMMN.k(593) + GloftMMN.k(this.a[1]));
                  if (f.av > 0 && f.d(h.s[f.av].b[13]) == -1) {
                     a((int)884);
                  }
               } else if (var14 == 289) {
                  a(GloftMMN.k(657) + " " + GloftMMN.k(this.a[1]) + GloftMMN.k(594));
               }
            } else if (var14 < var13) {
               if (var13 >= 287 && var14 <= 287) {
                  var4.c(36, -1);
               }

               if (var14 == 285 && var19 == 0 && f.bc[5] == 1) {
                  f(5, 0);
               }
            }
         }

         if (var1 == 42) {
            var19 = f.d(this.b[13]);
            if (var3 > 0 && var2 == 0) {
               if (var19 != 3 && var19 != 14 && var19 != 9 && var19 != 15 && var19 != 10 && var19 != 16) {
                  if (var19 != 20 && var19 != 21) {
                     if (var19 == 1) {
                        a((int)686);
                        f(6, 3);
                     }
                  } else if (f.bc[0] < 44) {
                     f(0, 44);
                     a((int)685);
                     au = true;
                  }
               } else if (f.bc[4] == 5) {
                  f(4, 7);
               }

               var4.c(37, 1);
               return;
            }

            if (var3 == 0 && var2 > 0 && var19 == 1) {
               f(6, 0);
               ag[0] = f.aT + 10080000L;
               a((int)687);
            }
         }

      }
   }

   final void g() {
      boolean var1 = this.b[6] == 0;
      g var2 = h.s[0];

      for(int var3 = 0; var3 < D; ++var3) {
         int var4;
         int var5 = var4 = this.a[var3];
         if (var3 == 40 && !var1) {
            var5 = 0;

            for(int var6 = 0; var6 < this.k.length; ++var6) {
               var5 += var2.a[this.k[var6]];
            }

            var5 = (var5 / this.k.length + this.a[39]) / 2;
         }

         if (var1) {
            if (var3 == 22 && var1) {
               a(var4 <= 39, 71);
            }

            if (var3 == 26) {
               if (var4 >= 50) {
                  a(true, 70);
                  v = 6;
               } else {
                  a(false, 70);
                  if (v == 6) {
                     v = 0;
                  }
               }
            }
         }

         this.a[var3] = var5;
      }

   }

   static void b(int var0) {
      g var1 = h.s[var0];
      g var2 = h.s[0];
      boolean var3 = false;
      int var9 = f.d(var1.b[13]);
      String var4 = null;
      String var5 = null;
      String var6 = null;
      au = true;
      if (var9 == -1) {
         if (var1.b[2] == 66 || var1.b[2] == 62) {
            a((int)623);
            return;
         }

         if (var1.b[2] == 69) {
            a((int)632);
            au = false;
            return;
         }

         if (a(247, var1, false)) {
            a((int)643);
            au = false;
            return;
         }
      }

      if (f.bc[1] < 10) {
         if ((var9 == 2 || var9 == 13) && f.bc[1] == 1) {
            var4 = GloftMMN.k(598);
         } else if ((var9 == 2 || var9 == 13) && f.bc[1] == 2) {
            var4 = GloftMMN.k(599);
         }
      }

      if (var9 == 1) {
         if (f.bc[0] == 0) {
            f(0, 1);
            f(1, 10);
            var4 = GloftMMN.k(601);
            var5 = GloftMMN.k(602);
            f(6, 0);
            ag[0] = f.aT + 4320000L;
            if (h.s[0].a[0] == 1) {
               a(GloftMMN.k(606) + " " + GloftMMN.k(607), (int)2);
            } else {
               a(GloftMMN.k(606) + " " + GloftMMN.k(607), (int)13);
            }

            aa = 5;
            ah[5] = 1;
            ah[4] = 1;
            ah[12] = 1;
            ah[26] = 1;
            ah[25] = 1;
            ah[33] = 1;
            au = true;
         } else if (f.bc[6] == 1) {
            if (var2.a[35] >= 100) {
               var4 = GloftMMN.k(604);
               var5 = GloftMMN.k(605);
               var2.c(35, -100);
               ag[0] = f.aT + 10080000L;
               f(6, 0);
               if (f.bc[0] <= 6) {
                  f(0, 8);
                  if (h.s[0].a[0] == 1) {
                     a(GloftMMN.k(617) + " " + GloftMMN.k(618) + " " + GloftMMN.k(619), (int)2);
                  } else {
                     a(GloftMMN.k(617) + " " + GloftMMN.k(618) + " " + GloftMMN.k(619), (int)13);
                  }

                  ae |= 512;
               }
            } else {
               var4 = GloftMMN.k(603);
            }
         } else if (f.bc[6] == 0) {
            au = false;
            a((int)654);
         }
      } else if (f.bc[0] == 3 && var9 == -1) {
         f(0, 4);
         var4 = GloftMMN.k(608);
      } else if (var9 == 0 && f.bc[0] > 0 && f.bc[0] != 3 && f.bc[5] == 0) {
         var4 = GloftMMN.k(609);
         var5 = GloftMMN.k(610);
         var6 = GloftMMN.k(611);
         if (f.bc[0] < 6) {
            f(0, 3);
         }

         au = false;
      } else if ((var9 == 2 || var9 == 13) && f.bc[1] > 9) {
         if (f.bc[0] < 3) {
            var4 = GloftMMN.k(606);
            var5 = GloftMMN.k(607);
            int var10;
            if ((var10 = b(var1)) != -1 && var10 == -3) {
            }
         } else if (f.bc[0] >= 7 && f.bc[0] < 20) {
            var4 = GloftMMN.k(617);
            var5 = GloftMMN.k(618);
            var6 = GloftMMN.k(619);
         } else if (f.bc[0] == 23 && f.bc[7] == 14) {
            if (f.bc[8] < 4) {
               if (f.bc[8] == 3) {
                  a((int)665);
               }

               a((int)660);
               au = true;
               f(8, 1);
               return;
            }

            if (f.bc[8] == 4) {
               au = true;
               a((int)658);
               a((int)659);
               if (h.s[0].a[0] == 1) {
                  a(GloftMMN.k(658) + " " + GloftMMN.k(659), (int)2);
               } else {
                  a(GloftMMN.k(658) + " " + GloftMMN.k(659), (int)13);
               }

               f(0, 24);
            }
         } else {
            if (f.bc[0] == 24) {
               au = true;
               a((int)659);
               return;
            }

            if (f.bc[0] == 40 && f.bc[7] == 20) {
               au = true;
               a((int)688);
               return;
            }

            if (f.bc[0] == 40) {
               a((int)689);
               a((int)690);
               a((int)691);
               a((int)692);
               if (h.s[0].a[0] == 1) {
                  a(GloftMMN.k(692), (int)2);
               } else {
                  a(GloftMMN.k(692), (int)13);
               }

               f(0, 41);
               aa = 5;
               au = true;
               return;
            }

            if (f.bc[0] == 60) {
               a((int)693);
               a((int)694);
               if (h.s[0].a[0] == 1) {
                  a(GloftMMN.k(694), (int)2);
               } else {
                  a(GloftMMN.k(694), (int)13);
               }

               au = true;
               return;
            }
         }
      } else if (var9 == 4 && var1.b[2] == 60 && f.bc[3] < 2) {
         var4 = GloftMMN.k(624);
      } else if (var9 == 4 && var1.b[2] != 60 && f.bc[3] == 0) {
         var4 = GloftMMN.k(625);
         var5 = GloftMMN.k(630);
         b(var1);
         f(3, 1);
         var1.a[39] = 10;
         au = false;
      } else if (var9 == 6) {
         if (var2.a[16] < 35) {
            var4 = GloftMMN.k(650);
            var5 = GloftMMN.k(695);
         } else if (var2.a[35] < 10) {
            var4 = GloftMMN.k(656);
         } else if (a(28, var1, false)) {
            a((int)354);
         } else {
            var4 = GloftMMN.k(651);
            if (f.bb[5] == 0) {
               var2.c(35, -10);
               g(5, 1);
            }
         }
      } else if (var9 == 8) {
         if (a(272, var1, false)) {
            g(5, 1);
         } else {
            if (ah[19] == 0) {
               var4 = GloftMMN.k(653);
            } else {
               var4 = GloftMMN.k(748);
               au = true;
            }

            if (a(28, var1, false)) {
               var5 = GloftMMN.k(354);
            } else if (f.bc[8] == 4 && f.bc[7] == 14) {
               au = false;
            }
         }
      } else if ((var9 == 9 || var9 == 15) && h.H == 11) {
         if (f.bc[0] > 6 && f.bc[0] < 9) {
            var4 = GloftMMN.k(666);
            var5 = GloftMMN.k(622);
            f(0, 9);
         } else if (f.bc[4] == 2) {
            var4 = GloftMMN.k(631);
         } else if (f.bb[0] == 1) {
            var4 = GloftMMN.k(667);
            var6 = GloftMMN.k(668);
         } else if (f.bb[0] == 0) {
            GloftMMN.k(632);
            var4 = GloftMMN.k(669);
            au = false;
         }
      } else if ((var9 == 10 || var9 == 16) && h.H == 11) {
         if (f.bc[0] > 6 && f.bc[0] < 10) {
            var4 = GloftMMN.k(666);
            var5 = GloftMMN.k(622);
            f(0, 9);
         } else if (f.bc[4] == 2) {
            var4 = GloftMMN.k(631);
         } else if (f.bb[0] == 1) {
            var4 = GloftMMN.k(670);
            var5 = GloftMMN.k(671);
         } else if (f.bb[0] == 0) {
            var4 = GloftMMN.k(632);
            var5 = GloftMMN.k(669);
            au = false;
         }
      } else if ((var9 == 3 || var9 == 14) && h.H == 11) {
         if (f.bc[11] != 1) {
            if (f.bc[7] == 11) {
               if (var2.a[30] >= 35) {
                  if (f.bb[9] == 0) {
                     a(GloftMMN.k(922), (byte)3);
                  } else if (f.bb[9] == 2) {
                     var4 = GloftMMN.k(707);
                  } else {
                     var4 = GloftMMN.k(705);
                  }
               } else {
                  var4 = GloftMMN.k(923);
               }
            }
         } else if (f.bc[0] > 6 && f.bc[0] < 10) {
            var4 = GloftMMN.k(666);
            var5 = GloftMMN.k(622);
            f(0, 9);
         } else if (f.bc[0] == 11) {
            var4 = GloftMMN.k(631);
         } else if (f.bb[0] == 1) {
            var4 = GloftMMN.k(620);
            var5 = GloftMMN.k(621);
         } else if (f.bb[0] == 0) {
            var4 = GloftMMN.k(669);
            au = false;
         } else if (f.bc[4] == 2) {
            var4 = GloftMMN.k(631);
         }
      } else if (var9 == 11 && f.bc[0] == 26) {
         var4 = GloftMMN.k(696);
         var5 = GloftMMN.k(697);
         f(0, 27);
         au = true;
      } else if (var9 == 11 && f.bc[0] == 27) {
         var4 = GloftMMN.k(698);
         var5 = GloftMMN.k(699);
         f(0, 28);
         a(GloftMMN.k(699), (int)11);
         aa = 0;
         au = true;
      } else if (var9 == 11 && f.bc[0] == 28) {
         var4 = GloftMMN.k(700);
         var5 = GloftMMN.k(701);
         ah[21] = 1;
         f[21] = 1;
         f(0, 29);
         if (h.s[0].a[0] == 1) {
            a(GloftMMN.k(702), (int)2);
         } else {
            a(GloftMMN.k(702), (int)13);
         }

         aa = 5;
      } else if (var9 == 11 && f.bc[0] == 29) {
         var4 = GloftMMN.k(703);
      } else if (var9 == 12 && f.bc[0] == 29) {
         if (var2.a[13] < 25) {
            var4 = GloftMMN.k(769);
            var5 = GloftMMN.k(770);
            au = true;
         } else {
            var4 = GloftMMN.k(704);
            au = false;
         }
      } else if (var9 == 17) {
         if (f.bc[7] == 29) {
            if (var2.a[16] >= 35) {
               if (f.bb[9] == 0) {
                  a(GloftMMN.k(705), (byte)2);
               } else if (f.bb[9] == 2) {
                  var4 = GloftMMN.k(707);
               } else {
                  var4 = GloftMMN.k(705);
               }
            } else {
               var4 = GloftMMN.k(708);
            }
         } else if (f.bc[7] == 32) {
            if (var2.a[13] >= 45) {
               if (f.bb[9] == 0) {
                  var4 = GloftMMN.k(709);
                  var5 = GloftMMN.k(710);
                  g(9, 1);
               } else if (f.bb[9] == 2) {
                  var4 = GloftMMN.k(707);
               } else {
                  var4 = GloftMMN.k(710);
               }
            } else {
               var4 = GloftMMN.k(711);
            }
         }
      } else if (var9 == 19) {
         var1.a();
         if (f.bc[0] == 41) {
            var4 = GloftMMN.k(712);
            var5 = GloftMMN.k(713);
            au = false;
         } else if (f.bc[0] == 42) {
            if (var2.a[35] < 500) {
               a((int)733);
               au = true;
               return;
            }

            a((int)714);
            var2.c(35, -500);
            f(0, 43);
            au = true;
            return;
         }
      } else if (var9 != 20 && var9 != 21) {
         if (var9 == 22) {
            if (var2.a[16] < 60) {
               a((int)771);
               au = true;
               return;
            }

            if (f.bc[0] == 45) {
               a((int)722);
               a((int)723);
               a((int)724);
               au = false;
               var1.a();
            } else if (f.bc[0] == 46) {
               if (var2.a[35] >= 500) {
                  a((int)726);
                  var2.c(35, -500);
                  f(0, 60);
                  if (h.s[0].a[0] == 1) {
                     a(GloftMMN.k(727), (int)2);
                  } else {
                     a(GloftMMN.k(727), (int)13);
                  }

                  aa = 2;
                  au = true;
                  return;
               }

               a((int)725);
            }
         } else if (var9 == 18) {
            var4 = GloftMMN.k(731);
            if (f.bb[10] == 0) {
               if (var2.a[24] < 60) {
                  if (f.bc[12] <= 0) {
                     a(GloftMMN.k(728), (byte)1);
                     var4 = null;
                  }
               } else if (var2.a[24] < 80) {
                  var4 = GloftMMN.k(729);
                  au = false;
               } else if (var2.a[24] < 90) {
                  var4 = GloftMMN.k(730);
                  au = false;
               }
            }
         } else if (var9 == 3 || var9 == 14 || var9 == 9 || var9 == 15 || var9 == 10 || var9 == 16) {
            if (f.bc[0] < 20) {
               if (f.bc[4] != 2 && f.bc[4] != 3) {
                  if (f.bc[4] == 4 && f.bb[0] == 0) {
                     var4 = GloftMMN.k(635);
                  } else if (f.bc[4] == 4 && f.bb[0] == 1) {
                     var4 = GloftMMN.k(640);
                     f(4, 5);
                     au = false;
                  } else if (f.bc[4] == 5) {
                     if (a(85, var1, false)) {
                        var4 = GloftMMN.k(586);
                     } else {
                        var4 = GloftMMN.k(916);
                     }

                     au = false;
                  }
               } else {
                  var4 = GloftMMN.k(634);
                  f(4, 3);
                  au = false;
               }
            } else if (f.bc[0] < 30) {
               if (f.bc[4] == 8) {
                  f(4, 9);
                  var4 = GloftMMN.k(642);
                  if (a(247, var1, false)) {
                     var5 = GloftMMN.k(643);
                  }

                  au = false;
               } else if (a(247, var1, false)) {
                  a((int)643);
                  au = false;
               }
            }
         }
      } else if (f.bc[0] == 43) {
         a((int)715);
         a((int)716);
         au = false;
         var1.a();
      } else if (f.bc[0] >= 44 && f.bc[0] < 60) {
         a((int)685);
         au = true;
      } else {
         if (f.bc[0] == 60) {
            a((int)717);
            a((int)718);
            if (h.s[0].a[0] == 1) {
               a(GloftMMN.k(718), (int)21);
            } else {
               a(GloftMMN.k(718), (int)20);
            }

            f(0, 61);
            ah[24] = 1;
            f[24] = 1;
            aa = 1;
            au = true;
            return;
         }

         if (f.bc[0] == 61 && f.bc[7] != 24) {
            a((int)718);
         } else if (f.bc[0] == 61) {
            var4 = GloftMMN.k(720);
            var2.a[33] = 1;
            f(0, 62);
            boolean var8 = false;
            if (h.s[0].a[0] == 1) {
               a(GloftMMN.k(924), (int)2);
            } else {
               a(GloftMMN.k(924), (int)13);
            }

            aa = -1;
         }
      }

      if (var9 == -1) {
         if (var9 == 7 && a(15, var1, false)) {
            a((int)646);
         }

         if (a(59, var1, false)) {
            a((int)775);
         } else {
            if (a(253, var1, false) && !a(236, var1, false)) {
               if (a(64, var1, false)) {
                  a((int)773);
               } else if (a(63, var1, false) && !a(20, var1, false)) {
                  a((int)774);
               } else {
                  a((int)645);
               }
            } else if (a(252, var1, false)) {
               a((int)644);
            }

            au = false;
         }
      }

      if (var4 != null) {
         a(var4);
         if (var5 != null) {
            a(var5);
         }

         if (var6 != null) {
            a(var6);
            return;
         }
      } else {
         au = false;
      }

   }

   static void f(int var0, int var1) {
      byte var2 = f.bc[var0];
      byte var3 = 13;
      if (h.s[0].a[0] == 1) {
         var3 = 2;
      }

      switch(var0) {
      case 0:
         if (var1 == 42) {
            a((int)732);
            a((int)733);
            a(GloftMMN.k(733), (int)19);
            au = true;
         } else if (var1 == 43) {
            a(GloftMMN.k(734), (int)var3);
            aa = 6;
            ah[31] = 1;
         } else if (var1 == 44) {
            if (f.bj < 2000) {
               a(GloftMMN.k(777) + GloftMMN.k(778), (int)var3);
               aa = 2;
            } else {
               var1 = 45;
               a(GloftMMN.k(779), (int)var3);
               aa = 5;
            }
         } else if (var1 == 45) {
            a(GloftMMN.k(735), (int)var3);
            aa = 5;
         }

         if (var1 > var2 && h.ai == 1) {
            h.aj = true;
         }
         break;
      case 1:
         if (var2 == 1 && var1 == 2) {
            a((int)613);
         } else if (var2 < 3 && var1 == 3) {
            ah[1] = 1;
            ah[0] = 1;
            a((int)614);
            h.m = 0;
         }
      case 2:
      case 3:
      case 7:
      case 8:
      case 9:
      case 10:
      case 12:
      default:
         break;
      case 4:
         if (var1 == 4 && f.bb[0] == 0) {
            a((int)635);
            au = true;
         } else if (var1 == 7) {
            f(0, 20);
            var1 = 8;
            a((int)641);
            h.m = 1;
            a(GloftMMN.k(672), (int)var3);
            aa = 0;
            f.bc[31] = 1;
         }
         break;
      case 5:
         if (var1 == 1) {
            a((int)655);
         }
         break;
      case 6:
         if (var1 == 0 && var2 == 1) {
            ah[0] = 1;
            ah[1] = 1;
         }
         break;
      case 11:
         if (var1 > 0 && var2 == 0 && f.bc[0] == 8) {
            a(GloftMMN.k(674), (int)var3);
            aa = 6;
            ah[11] = 1;
            f[11] = 1;
         }
         break;
      case 13:
         if (var1 == 1 && var2 == 0) {
            ag[4] = f.aT + 1440000L;
            a((int)394);
            a((int)902);
            h.m = 25;
            f.bc[31] = 1;
         }
      }

      f.bc[var0] = (byte)var1;
   }

   static void g(int var0, int var1) {
      switch(var0) {
      case 0:
         if (h.H != 0 && h.H != 9 && h.H != 17 && h.H != 30) {
            if (var1 == 1) {
               GloftMMN.e();
               GloftMMN.c(4);
            } else if (var1 == 0 && GloftMMN.b(0, 4)) {
               GloftMMN.d(0);
            }
         }
         break;
      case 2:
         if (var1 == 1) {
            a((int)736);
         }
         break;
      case 5:
         if (var1 == 1) {
            g(4, 1);
            if (f.bc[0] == 21) {
               f(0, 22);
            } else if (f.bc[0] == 22) {
               f(0, 23);
            }
         } else {
            g(4, 0);
         }
         break;
      case 12:
         if (var1 == 1) {
            a((int)919);
         }
      }

      f.bb[var0] = (byte)var1;
   }

   static void a(g var0) {
      g var1 = h.s[0];
      byte var2 = h.E[h.D - 2];
      byte var3 = h.E[h.D - 1];
      byte var4 = h.E[h.D];
      int var5 = f.d(var0.b[13]);
      if (var2 == 1 && var3 == 1 && var4 == 0 && h.w > 0) {
         var0.c(26, 30);
         au = true;
         if ((var5 == 3 || var5 == 14 || var5 == 9 || var5 == 15 || var5 == 10 || var5 == 16) && f.bc[4] == 3) {
            f(4, 4);
         }
      }

      if (var2 == 1 && var3 == 0 && var4 == 0) {
         if (h.w > 0 && f.bc[4] == 4 && f.bb[0] == 1 && (var5 == 3 || var5 == 14 || var5 == 9 || var5 == 15 || var5 == 10 || var5 == 16)) {
            f(4, 5);
            au = true;
         }

         au = true;
      }

      if (var2 == 2 && var3 == 2 && var4 == -1 && h.w > 0) {
         if ((var5 == 3 || var5 == 14 || var5 == 9 || var5 == 15 || var5 == 10 || var5 == 16) && f.bc[4] == 9) {
            f(4, 11);
            f(0, 22);
            ag[3] = f.aT + 7000L;
            f(11, 0);
         }

         a((int)749);
         au = true;
      }

      if (var2 == 2 && var3 == 3 && var4 == -1 && var0.a[42] > 0) {
         if ((var5 == 3 || var5 == 14 || var5 == 9 || var5 == 15 || var5 == 10 || var5 == 16) && f.bc[4] == 9) {
            f(4, 12);
            a((int)652);
            h.m = 0;
            aa = 5;
            if (h.s[0].a[0] == 1) {
               a(GloftMMN.k(647) + " " + GloftMMN.k(648) + " " + GloftMMN.k(649), (int)2);
            } else {
               a(GloftMMN.k(647) + " " + GloftMMN.k(648) + " " + GloftMMN.k(649), (int)13);
            }

            f(0, 22);
            f.bc[31] = 0;
         }

         f(11, 0);
         var0.c(42, -var0.a[42]);
         var1.c(38, 1);
      }

      if (var2 == 1 && var3 == 1 && h.w > 0 && a(236, var0, false)) {
         var0.c(39, -var0.a[39]);
      } else if (var2 >= 0 && a(236, var0, false) && f.bc[13] == 0) {
         a((int)737);
      }

      if (var5 == 8 && var2 == 1 && var3 == 1 && var4 == 1 && h.w > 0) {
         g(4, 1);
         if (f.bc[0] == 24) {
            f(0, 25);
         }

         ah[19] = 1;
         a((int)664);
         au = true;
      }

      if (var5 == 18 && h.w > 0) {
         if (var2 == 1 && var3 == 1 && var4 == 4) {
            var1.c(35, 300);
            g(10, 1);
            au = true;
         } else if (var2 == 1 && var3 == 1 && var4 == 3) {
            var1.c(35, 500);
            g(10, 1);
            au = true;
         }
      }

      if (var2 == 1 && var3 == 1 && var4 == 5 && h.w > 0 && f.bc[12] > 0) {
         f(12, 0);
         var0.c(33, -1);
         a((int)738);
         a((int)739);
      }

      if (var2 >= 0 && h.w < 1 && a(58, var0, false) && var5 == 11) {
         a((int)740);
         a((int)741);
         au = true;
      }

   }

   static void a(String var0, byte var1) {
      ak = var0;
      al = var1;
   }

   static void a(int var0, boolean var1) {
      g var2 = h.s[0];
      switch(var0) {
      case 1:
         if (var1) {
            if (var2.a[35] >= 10) {
               f(12, 1);
               var2.c(35, -10);
               a((int)750);
               g(10, 1);
            } else {
               a((int)434);
            }

            au = true;
            return;
         }
         break;
      case 2:
         if (var1) {
            g(9, 1);
            a((int)706);
            au = true;
            return;
         }
         break;
      case 3:
         if (var1) {
            g(9, 1);
            a((int)763);
            a((int)764);
            au = true;
         }
      }

   }

   static void c(int var0) {
      if (ai[var0]) {
         ai[var0] = false;
         switch(var0) {
         case 4:
            ah[6] = 1;
            f[6] = 1;
            if (ah[7] == 0) {
               System.out.println("Auto barrrrrrrrr");
               a((int)751);
               return;
            }
         case 5:
         case 8:
         case 9:
         case 10:
         case 13:
         case 14:
         case 15:
         case 16:
         case 17:
         case 19:
         case 21:
         case 23:
         case 25:
         case 26:
         case 27:
         case 28:
         case 29:
         case 31:
         case 32:
         case 33:
         default:
            break;
         case 6:
            ah[9] = 1;
            return;
         case 7:
            ah[16] = 1;
            ah[32] = 1;
            f[32] = 1;
            ah[33] = 1;
            f[33] = 1;
            return;
         case 11:
            ah[30] = 1;
            f[30] = 1;
            a((int)765);
            return;
         case 12:
            ah[18] = 1;
            f[18] = 1;
            ah[13] = 1;
            ah[7] = 1;
            f[7] = 1;
            ah[14] = 1;
            f[26] = 1;
            ah[26] = 1;
            return;
         case 18:
            f[29] = 1;
            return;
         case 20:
            ah[22] = 1;
            f[22] = 1;
            ah[37] = 1;
            f[37] = 1;
            return;
         case 22:
            ah[34] = 1;
            f[34] = 1;
            ah[23] = 1;
            f[23] = 1;
            ah[22] = 1;
            f[22] = 1;
            return;
         case 24:
            ah[27] = 1;
            break;
         case 30:
            ah[28] = 1;
            f[28] = 1;
            return;
         case 34:
            ah[35] = 1;
            f[35] = 1;
            return;
         }

      }
   }

   static void d(int var0) {
      if (var0 == 30) {
         a((int)742);
      }

      if (var0 == 30) {
         a((int)743);
      }

   }

   static void h() {
      if (f.bc[4] == 2 && f.bc[0] < 11 && f.bc[14] == 1) {
         f(0, 11);
         aa = 5;
         if (h.s[0].a[0] == 1) {
            a(GloftMMN.k(744), (int)2);
         } else {
            a(GloftMMN.k(744), (int)13);
         }
      }

      f(14, 0);
   }

   static void i() {
      if (f.bc[0] >= 30) {
         if (f.bj >= 2000 && f.bc[0] == 44) {
            a(GloftMMN.k(766));
            f(0, 45);
            return;
         }

         a(GloftMMN.k(920) + "$ " + f.bj + " !");
      }

   }

   static boolean e(int var0) {
      if (var0 == 18 && f.bc[1] < 2) {
         a((int)504);
         return false;
      } else {
         if (var0 == 57) {
            if (f.bb[9] == 0 || f.bb[9] == 2) {
               a((int)745);
               return false;
            }
         } else if (var0 == 63) {
            if (f.bb[9] == 0 || f.bb[9] == 2) {
               a((int)767);
               return false;
            }
         } else if (var0 == 60 && (f.bb[9] == 0 || f.bb[9] == 2)) {
            a((int)768);
            return false;
         }

         return true;
      }
   }

   private void r() {
      if (this.an == null) {
         this.an = new byte[40];
      }

      for(int var1 = 0; var1 < 8; ++var1) {
         this.an[var1 * 5 + 0] = 0;
      }

   }

   private void a(byte var1, byte var2, int var3) {
      int var5 = 0;
      int var6 = -1;

      for(int var4 = 0; var4 < 8; ++var4) {
         if (this.an[var5 + 0] == 1) {
            if (this.an[var5 + 1] == var1) {
               if (this.an[var5 + 2] == var2) {
                  return;
               }

               var6 = var4;
            }
         } else if (var6 == -1) {
            var6 = var4;
         }

         var5 += 5;
      }

      if (var6 != -1) {
         var5 = var6 * 5;
         this.an[var5 + 0] = 1;
         this.an[var5 + 1] = var1;
         this.an[var5 + 2] = var2;
         this.an[var5 + 3] = 1;
         this.an[var5 + 4] = (byte)(var3 - 798);
      }
   }

   private void a(byte var1) {
      int var3 = 0;

      for(int var2 = 0; var2 < 8; ++var2) {
         if (this.an[var3 + 1] == var1) {
            this.an[var3 + 0] = 0;
            return;
         }

         var3 += 5;
      }

   }

   static void a(Graphics var0) {
      var0.setClip(0, 75, 240, 240);
      g var1 = h.s[0];
      if (S == null) {
         S = new d(h.u[1], 0, 0, (d)null);
         T = new d(h.u[1], 0, 0, (d)null);
         S.e = -1;
      }

      byte[] var2 = var1.an;
      int var4 = -1;
      int var5 = 0;

      for(int var3 = 7; var3 >= 0; --var3) {
         var5 = var3 * 5;
         if (var2[var5 + 0] > 0) {
            var4 = var3;
            break;
         }
      }

      if (var4 != -1) {
         if (f.aS.size() <= 0 && N.size() == 0) {
            f.a((String)GloftMMN.k(var2[var5 + 4] + 798), 0);
            ao = var2[var5 + 1];
         }

         var5 = var4 * 5;
         if (p >= 1500L) {
            if (S.e == -1) {
               S.a(27);
            } else if (S.e == 27 && S.a()) {
               S.a(28);
            }

            S.a = h.n[0].a + 1792;
            S.b = h.n[0].b - 16128 + 2048;
            S.a(var0);
            if (S.e != 28 || !S.a()) {
               S.b();
            }

            if (S.e == 28) {
               int[] var9 = S.c();
               T.a = var9[0];
               T.b = var9[1];
               T.a(var2[var5 + 1]);
               T.a(var0);
               T.b();
               return;
            }
         } else {
            S.e = -1;
            T.e = -1;
         }

      }
   }

   private static int h(int var0) {
      return ap[2 + var0 * 2 + 0] & 255 | (ap[2 + var0 * 2 + 1] & 255) << 8;
   }

   static void j() {
      ap = GloftMMN.j(4);
   }

   private static int a(int var0, g var1) {
      g var2 = h.s[0];
      int var3 = var0 & 3;
      int var4 = var0 >> 2;
      if (var3 == 0) {
         return var1.a[var4];
      } else if (var3 == 1) {
         return var2.a[var4];
      } else if (var3 == 3) {
         var4 = var0 >> 3;
         return (var0 & 4) >> 2 == 0 ? f.bb[var4] : f.bc[var4];
      } else {
         return 0;
      }
   }

   static boolean a(int var0, g var1, boolean var2) {
      if (var2) {
         for(int var3 = 0; var3 < 16; ++var3) {
            aq[var3] = -1;
         }

         ar = 0;
      }

      return a(var0, var1, var2, false);
   }

   private static boolean a(int var0, g var1, boolean var2, boolean var3) {
      int var5 = h(var0);
      boolean var6 = false;
      int var7;
      int var8 = (var7 = ap[var5 + 0] & 255) & 3;
      int var9;
      if ((var9 = var7 >> 3) == 0) {
         var9 = 1;
      }

      int var10 = ap[var5 + 1 + 0] & 255 | (ap[var5 + 1 + 1] & 255) << 8;
      var5 += 3;
      boolean var11 = false;
      if (var8 == 1) {
         var11 = true;
      }

      for(int var12 = 0; var12 < var9; ++var12) {
         int var13;
         int var15;
         switch(var13 = ap[var5++] & 255) {
         case 0:
            var15 = ap[var5++] & 255;
            byte var22 = ap[var5++];
            byte var17 = ap[var5++];
            int var14;
            if ((var14 = a(var15, var1)) >= var22 && var14 <= var17) {
               var6 = true;
               break;
            }

            var6 = false;
            break;
         case 1:
         case 2:
         case 3:
         case 4:
         case 5:
         case 6:
         default:
            int var20 = a(ap[var5++] & 255, var1);
            int var21 = a(ap[var5++] & 255, var1);
            if (var13 == 1) {
               var6 = var20 > var21;
            } else if (var13 == 2) {
               var6 = var20 >= var21;
            } else if (var13 == 3) {
               var6 = var20 < var21;
            } else if (var13 == 4) {
               var6 = var20 <= var21;
            } else if (var13 == 5) {
               var11 = var20 != var21;
            } else if (var13 == 6) {
               var6 = var20 == var21;
            }
            break;
         case 7:
            int var18 = ap[var5++] & 255;
            var6 = a(ap[var5++] & 255 | (ap[var5++] & 255) << 8, var1, var2, var18 == 1);
            break;
         case 8:
            var6 = f.aQ == 0;
            break;
         case 9:
            var6 = f.aQ == 1;
            break;
         case 10:
            var6 = false;
            if (var1 != null) {
               var15 = ap[var5++] & 255;

               for(int var16 = 0; var16 < var1.k.length; ++var16) {
                  if (var1.k[var16] == var15) {
                     var6 = true;
                     break;
                  }
               }
            }
         }

         if (var8 == 0) {
            var11 = var6;
         } else if (var8 == 1) {
            var11 = var11 && var6;
         } else if (var8 == 2) {
            var11 = var11 || var6;
         }
      }

      if (var3) {
         var11 = !var11;
      }

      if (var2 && var11 && var10 != 65535) {
         aq[ar & 255] = (short)var10;
         ++ar;
      }

      return var11;
   }

   static int f(int var0) {
      return as[1 + var0 * 2 + 0] & 255 | (as[1 + var0 * 2 + 1] & 255) << 8;
   }

   static int h(int var0, int var1) {
      int var2 = f(var0) + 1;

      for(int var3 = 0; var3 < var1; ++var3) {
         var2 = f.a((g)null, as, var2 + 10, true, false);
      }

      return var2;
   }

   private static int i(int var0) {
      return at[1 + var0 * 2 + 0] & 255 | (at[1 + var0 * 2 + 1] & 255) << 8;
   }

   static void k() {
      as = GloftMMN.j(5);
      at = GloftMMN.j(6);
   }

   private static boolean s() {
      return h.y == 0 || h.y == 1 || h.y == 19 || h.y == 6;
   }

   static boolean l() {
      return h.y == 0 || h.y == 1 || h.y == 2 || h.y == 5 || h.y == 19 || h.y == 6 || h.y == 7 || h.y == 11 || h.y == 12;
   }

   static void m() {
      if (s()) {
         for(int var0 = 1; var0 < h.s.length; ++var0) {
            g var3;
            if ((var3 = h.s[var0]) != null && var3.s && (var3.b[13] < 0 || f.d(var3.b[13]) != 22 || var3.b[2] != 87)) {
               int var4;
               for(var4 = 0; var4 < (at[0] & 255); ++var4) {
                  int var1 = i(var4);
                  boolean var5 = false;
                  int var6 = f.aL[1 + var3.b[2] * 5 + 1] & 7;
                  int var2;
                  if (var3.b[3] == 1 || at[var1 + 1] >= var6) {
                     if ((var2 = at[var1 + 0] & 255) == 255) {
                        var5 = true;
                     } else {
                        var2 = (var2 = (var2 << 8) / 10) * var2 >> 8;
                        var5 = var3.t <= var2;
                     }
                  }

                  if (var5 && a(at[var1 + 2] & 255 | (at[var1 + 3] & 255) << 8, var3, false)) {
                     var2 = at[var1 + 4] & 255;
                     var1 += 5;

                     int var7;
                     for(var7 = 0; var7 < var2; ++var7) {
                        if (a(at[var1 + 0] & 255 | (at[var1 + 1] & 255) << 8, var3, false)) {
                           byte var9 = var3.b[2];
                           byte var10 = at[var1 + 2];
                           d var11 = h.n[0];
                           d var12 = h.n[var3.b[5]];
                           if (var10 != var9) {
                              var3.b[3] = 1;
                              if (var3.b[4] == -1) {
                                 var3.b[4] = var9;
                                 var3.b[7] = (byte)var12.c;
                              }

                              var3.a((int)var10, (int)-1, (int)-1);
                           }

                           if (var11.a > var12.a) {
                              var12.c = 0;
                           } else {
                              var12.c = 1;
                           }
                           break;
                        }

                        var1 += 3;
                     }

                     if (var7 != var2) {
                        break;
                     }
                  }
               }

               if (var4 == (at[0] & 255) && var3.b[3] == 1) {
                  if (var3.b[7] != -1) {
                     h.n[var3.b[5]].c = var3.b[7];
                     var3.b[7] = -1;
                  }

                  var3.a((int)var3.b[4], (int)-1, (int)-1);
                  var3.b[4] = -1;
                  var3.b[3] = 0;
                  f.c(var3.b[6] & 255);
               }
            }
         }

      }
   }

   static void a(Graphics var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7) {
      var0.setColor(var5);
      var0.fillRect(var1, var2, var3, var4);
      var0.setColor(var6);
      var0.fillRect(var1, var2, var3, 1);
      var0.fillRect(var1, var2 + var4 - 1, var3, 1);
      var0.fillRect(var1, var2, 1, var4);
      var0.fillRect(var1 + var3 - 1, var2, 1, var4);
      var0.setColor(var7);
      var0.fillRect(var1 + 3, var2 + var4, var3, 3);
      var0.fillRect(var1 + var3, var2 + 3, 3, var4);
   }

   static void a(Graphics var0, g var1, int var2, int var3) {
      var0.setClip(0, 75, 240, 240);
      byte var4 = 65;
      if (var1.a[39] < 0) {
         var4 = 63;
      } else if (var1.a[42] > 0) {
         var4 = 67;
      }

      int var6 = var1.a[39];
      int var7 = e(39, 0);
      int var8 = e(39, 10);
      int var9 = e(39, var6);

      int var5;
      for(var5 = 0; var5 < 5; ++var5) {
         h.u[1].a((Graphics)var0, var4, 0, var2 + 16 * var5, var3, 0, 0, 0);
      }

      int var12;
      if (var6 < 0) {
         boolean var10 = false;
         boolean var11 = false;
         int var13 = 62 * (Math.abs(var6) - 0) / 100;
         var13 += 16;
         var0.setClip(var2, 75, var13, 240);

         for(var5 = 0; var5 < 5; ++var5) {
            var12 = var2 + 16 * var5;
            h.u[1].a((Graphics)var0, var4 + 1, 0, var12, var3, 0, 0, 0);
         }
      } else if (var6 >= 10) {
         int var16;
         if ((var16 = var9 - var8) > 4) {
            var16 = 4;
         }

         int var17 = 0;

         for(var5 = 0; var5 <= var16; ++var5) {
            var17 = var2 + 16 * var5;
            h.u[1].a((Graphics)var0, var4 + 1, 0, var17, var3, 0, 0, 0);
         }

         if (var16 < 4) {
            var12 = F[39] + 7 + (var9 - var7 + 1) * 4;
            byte var18 = E[var12 + 2];
            byte var14 = E[var12 + 3];
            var17 += 16;
            int var15;
            if ((var15 = 42 * (var6 - var18) / (4 * (var14 - var18))) < 0) {
               var15 = 0;
            }

            var0.setClip(var17, 75, var15, 240);
            h.u[1].a((Graphics)var0, var4 + 1, 0, var17, var3, 0, 0, 0);
         }
      }

   }

   static void a(Graphics var0, boolean var1, int var2, boolean var3) {
      var0.setClip(0, 75, 240, 240);
      if (!var1) {
         a(var0, 3, 77, 37, 37, 4891536, 0, 0);
         a(var0, 38, 96, 197, 18, 3635849, 0, 0);
         h.bf[0] = 3;
         h.bf[1] = 77;
         h.bf[2] = 38;
         h.bf[3] = 96;
         h.bg = true;
         g var5;
         byte var6 = (var5 = h.s[f.av]).b[5];
         h.n[var6].d.m = var5.b[15];
         h.n[var6].d.a((Graphics)var0, 66, 0, 21, 95, 0, 0, 0);
         String var7 = GloftMMN.k(var5.a[1]);
         var0.setClip(44, 96, 109, 18);
         int var8 = 44;
         if (var3 && R >= 1000L) {
            var5.q();
            String var9 = GloftMMN.k(836);
            var7 = var7 + var9 + ab + var9 + GloftMMN.k(837) + " " + ac + var9;
            h.M.a(var7);
            int var10 = (int)((R - 1000L) * 44L) / 1000;
            var8 = 44 - var10 % (a.y + 109);
            var8 += 109;
         }

         h.M.a(var0, var7, var8, 98, 20, 0);
         a(var0, (g)var5, 155, 99);
         R += h.aH;
      } else {
         if (M != null) {
            a(var0, 3, 112, 232, 80, 16777215, 0, 0);
            Q = a(var0, (String)M, Q, 115);
            if (h.K >= 4 && (h.K - 4) % 4 < 2) {
               h.v[7].d.a((Graphics)var0, 31, 0, 227, 186, 0, 0, 0);
            }
         }

      }
   }

   static void b(Graphics var0) {
      var0.setClip(0, 75, 240, 240);
      if (N.size() > 0) {
         int var1;
         int var2;
         String var3;
         String var4;
         if ((var4 = (String)N.elementAt(0)).charAt(0) == '#') {
            au = true;
            if (h.K == 0) {
               GloftMMN.c(2);
            }

            var3 = GloftMMN.k(841);
            var4 = var4.substring(1, var4.length());
            var1 = 16711680;
            var2 = 0;
         } else {
            if (var4.charAt(0) == '!') {
               h.z = 8;
               return;
            }

            int var7;
            if (var4.charAt(0) == '%') {
               if (f.av <= 0) {
                  h.z = 8;
                  return;
               }

               g var15;
               int var16 = (var15 = h.s[f.av]).a[41];

               for(var7 = 0; var7 < h.d.length && (var16 < h.d[var7 + 0] || var16 < h.d[var7 + 0]); var7 += 4) {
               }

               var4 = var4 + GloftMMN.k(var15.a[1]) + GloftMMN.k(886);
               h.z = 8;
               int var8;
               if ((var8 = h.d[var7 + 2]) > 0) {
                  var4 = var4 + Integer.toString(var8) + " $";
                  h.s[0].c(35, var8);
               } else {
                  var7 = h.d[var7 + 3];
                  g var12;
                  int var13 = (var12 = h.s[0]).a[0];
                  int var14 = 0;
                  int var9;
                  int var10;
                  int var11;
                  int[] var10000;
                  if (var7 == 1) {
                     var9 = var12.a[6];
                     var11 = h.a[var13].length / 3;

                     do {
                        var10 = GloftMMN.a(0, var11, var9);
                        ++var14;
                     } while(var14 < 32 && b(var10, -1) != -1);

                     V = (byte)((var12.a[6] + 1) * -1);
                     W = (byte)var12.a[7];
                     var12.a[6] = var10;
                     var10000 = var12.a;
                     var10000[7] += GloftMMN.c(1, 255);
                     var10000 = var12.a;
                     var10000[7] %= h.p[var13][2].l;
                     var4 = var4 + GloftMMN.k(883);
                     a((int)var10, (int)-1, (byte)((byte)var12.a[7]));
                  } else if (var7 == 2) {
                     var9 = var12.a[8];
                     var11 = h.p[var13][4].a / h.r[var13][4];

                     do {
                        var10 = GloftMMN.a(0, var11, var9);
                        ++var14;
                     } while(var14 < 32 && b(-1, var10) != -1);

                     V = (byte)(var12.a[8] + 1);
                     W = (byte)var12.a[9];
                     var12.a[8] = var10;
                     var10000 = var12.a;
                     var10000[9] += GloftMMN.c(1, 255);
                     var10000 = var12.a;
                     var10000[9] %= h.p[var13][4].l;
                     var4 = var4 + GloftMMN.k(883);
                     a((int)-1, (int)var10, (byte)((byte)var12.a[7]));
                  }

                  h.i = 0;
                  h.z = 68;
               }

               N.setElementAt(var4, 0);
               return;
            }

            if (var4.charAt(0) == '~') {
               g var6 = null;
               var7 = f.av;
               if (f.av == -1) {
                  for(int var5 = 1; var5 < h.s.length; ++var5) {
                     if (h.s[var5] != null && (h.s[var5].e != null || h.s[var5].a[43] >= 0) && h.s[var5].b[20] == 1) {
                        var6 = h.s[var5];
                        f.av = var5;
                        break;
                     }
                  }
               } else {
                  var6 = h.s[f.av];
               }

               if (var6 == null) {
                  h.i = 255;
                  return;
               }

               var0.setClip(0, 0, 240, 400);
               a(var0, false, 103, false);
               M = b(var4.substring(1, var4.length()), 207);
               a(var0, true, 103, false);
               f.av = var7;
               return;
            }

            var3 = GloftMMN.k(842);
            var1 = 0;
            var2 = 11632793;
         }

         a(var0, 3, 155, 229, 19, var2, var1, var1);
         h.M.a(var0, var3, 120, 156, 17, 0);
         a(var0, 3, 171, 229, 83, -1, var1, var1);
         var4 = b(var4, 207);
         Q = a(var0, (String)var4, Q, 174);
         if (h.K >= 4 && (h.K - 4) % 4 < 2) {
            h.v[7].d.a((Graphics)var0, 31, 0, 224, 248, 0, 0, 0);
         }
      }

   }

   private static int a(Graphics var0, String var1, int var2, int var3) {
      h.M.a(var0, var1, 116, var3, 17, 1);
      return var1.length() << 1;
   }

   static String b(String var0, int var1) {
      int var2 = 0;
      int var3 = 1;
      String var4 = "";

      do {
         h.M.a(var0.substring(var2, var3));
         if (a.y < var1) {
            ++var3;
         } else {
            if (var0.charAt(var3) == ' ') {
               --var3;
            }

            while(var3 >= 0 && var0.charAt(var3) != ' ' && var0.charAt(var3) != '\n') {
               --var3;
            }

            var4 = var4 + var0.substring(var2, var3) + "\n";
            var3 = (var2 = var3 + 1) + 1;
         }
      } while(var3 < var0.length());

      return var4 + var0.substring(var2, var0.length());
   }

   static int b(g var0) {
      int var1 = var0.b[6] & 255;
      boolean var2 = f.d(var0.b[13]) != -1;

      int var3;
      for(var3 = 0; var3 < 25; ++var3) {
         if (G[var3] == var1) {
            return -2;
         }
      }

      if (!var2 && t()) {
         return -3;
      } else {
         var3 = -1;

         for(int var4 = 0; var4 < 25; ++var4) {
            if (G[var4] < 1) {
               var3 = var4;
               break;
            }
         }

         if (var3 == -1) {
            return -1;
         } else {
            G[var3] = var1;
            var0.a();
            return 1;
         }
      }
   }

   private static boolean t() {
      int var0 = 0;

      for(int var1 = 0; var1 < 25; ++var1) {
         if (G[var1] > 0 && f.d(h.s[G[var1]].b[13]) == -1) {
            ++var0;
         }
      }

      if (var0 >= 9) {
         return true;
      } else {
         return false;
      }
   }

   private static boolean c(g var0) {
      for(int var1 = 0; var1 < 25; ++var1) {
         if (G[var1] == (var0.b[6] & 255)) {
            return true;
         }
      }

      return false;
   }

   static void a(String var0) {
      N.addElement(var0);
   }

   static void a(boolean var0, int var1) {
      if (var0) {
         if (var1 > u) {
            u = var1;
            return;
         }
      } else if (u == var1) {
         u = -1;
      }

   }

   final void n() {
      d var1 = h.n[this.b[5] & 255];
      if (this.b[7] != -1) {
         var1.c = this.b[7];
      }

      this.a((int)this.b[2], (int)-1, (int)-1);
      f.c(this.b[6] & 255);
   }

   final void a(int var1, int var2, int var3) {
      d var4;
      (var4 = h.n[this.b[5] & 255]).e = -1;
      if (var1 < 0) {
         var1 = 0;
      }

      this.b[2] = (byte)var1;
      this.a[44] = var1;
      int var5 = 1 + var1 * 5;
      int var6 = f.aL[var5 + 0] & 255;
      this.b[11] = (byte)var6;
      if (var3 == -1) {
         var4.a(var6);
         this.b[8] = 0;
         this.b[9] = 0;
         this.b[10] = (byte)var4.b(var6);
         this.b[12] = 0;
      } else {
         int var7 = (f.aL[var5 + 1] & 255) >> 3;
         var5 = 1 + var3 * 5;
         int var8 = (f.aL[var5 + 1] & 255) >> 3;
         int var9 = f.aL[var5 + 0] & 255;
         int var10 = var4.b(var6);
         int var11 = var4.b(var9);
         int var12 = var8 + var10;
         if (var7 + var11 > var12) {
            var12 = var7 + var11;
         }

         if (var8 == 0) {
            var4.a(var6);
         } else {
            var4.a(0);
         }

         this.b[8] = (byte)var8;
         this.b[9] = 0;
         this.b[10] = (byte)var12;
      }

      if (var2 == 0) {
         var4.c = 0;
      } else {
         if (var2 == 1) {
            var4.c = 1;
         }

      }
   }

   final void o() {
      if (l()) {
         if (!this.A) {
            d var1 = h.n[this.b[5] & 255];
            byte var2;
            if ((var2 = this.b[12]) != 0) {
               g var3;
               if ((var3 = h.s[var2]).A) {
                  return;
               }

               if (this.b[3] != 1 && var3.b[3] == 1) {
                  var1.a(0);
                  return;
               }
            }

            int var5;
            int var12;
            if ((var12 = this.b[11] & 255) != 4 && var12 != 6) {
               int var13 = this.b[9] & 255;
               if (((var5 = this.b[8] & 255) != 0 || var13 != 0) && var13 != var5 - 1) {
                  if (var13 == (this.b[10] & 255) - 1) {
                     this.b[9] = 0;
                     return;
                  }

                  if (var1.a()) {
                     var1.a(0);
                  }
               } else {
                  var1.a(var12);
               }

               ++this.b[9];
            } else {
               if (var12 != 6 && f.av == this.b[6]) {
                  var1.a(0);
                  f.a(this, h.s[0]);
                  return;
               }

               short[] var4 = f.e[this.b[14] & 255];
               if (this.b[18] == -1) {
                  if (this.av == null) {
                     this.av = new int[2];
                  } else if (!c(this) && !h.au) {
                     this.b(this.b[13], false);
                     this.a(7 + this.a[0] * 2, this.b[6], false);
                  }

                  this.b[18] = (byte)var4[8];
                  short[] var10000 = f.k;
                  int var10001 = (this.c[0] >> 8) + (this.c[1] >> 8) * f.t;
                  var10000[var10001] &= -15873;
                  f.a(this.b[14], (int[])this.c);
                  this.a(this.c[0], this.c[1]);
               }

               f.a(var5 = f.a(97, this.b[18]), this.av);
               int var6 = this.c[0];
               int var7 = this.c[1];
               boolean var8 = this.a(this.av);
               int var9 = this.c[0];
               int var10 = this.c[1];
               this.c[0] = var6;
               this.c[1] = var7;
               this.a(var9, var10);
               if (var8) {
                  short var11;
                  if ((var11 = (var4 = f.e[var5])[4]) == -3) {
                     this.a((int)0, (int)-1, (int)-1);
                     return;
                  }

                  if (var11 == -2) {
                     this.b[20] = 0;
                     this.a(-255, -255);
                     return;
                  }

                  this.b[18] = (byte)var4[4];
                  return;
               }
            }

         }
      }
   }

   private static byte[] a(byte[] var0) {
      byte[] var6 = new byte[8];
      boolean var8 = false;
      int var1 = f.aK[C++] & 255;
      int var2 = f.aK[C++] & 255;
      int var3 = f.aK[C++] & 255;

      int var4;
      for(var4 = 0; var4 < var3; ++var4) {
         var6[var4] = f.aK[C++];
      }

      int var5;
      if (var0 != null) {
         int var9 = var3;

         for(var5 = 0; var5 < var0.length; ++var5) {
            for(var4 = 0; var4 < var3; ++var4) {
               if (var6[var4] == var0[var5]) {
                  --var9;
               }
            }
         }

         if (var2 > var9) {
            var2 = var9;
         }

         if (var1 > var2) {
            var1 = var2;
         }
      }

      int var10;
      if ((var10 = GloftMMN.c(var1, var2 + 1)) <= 0) {
         return null;
      } else {
         byte[] var7 = new byte[var10];

         label65:
         for(var4 = 0; var4 < var7.length; ++var4) {
            var7[var4] = -1;
            boolean var11 = false;

            while(true) {
               while(true) {
                  do {
                     if (var7[var4] != -1 && !var11) {
                        var6[var10] = -1;
                        continue label65;
                     }

                     var10 = GloftMMN.c(0, var3);
                     var7[var4] = var6[var10];
                     var11 = false;
                  } while(var0 == null);

                  for(var5 = 0; var5 < var0.length; ++var5) {
                     if (var7[var4] == var0[var5]) {
                        var11 = true;
                        break;
                     }
                  }
               }
            }
         }

         return var7;
      }
   }

   static boolean g(int var0) {
      switch(var0) {
      case 0:
      case 2:
      case 3:
      case 4:
      case 5:
      case 8:
      case 10:
      case 11:
      case 87:
      case 89:
      case 90:
      case 93:
      case 95:
      case 96:
         return false;
      case 1:
      case 6:
      case 7:
      case 9:
      case 12:
      case 13:
      case 88:
      case 91:
      case 92:
      case 94:
      case 97:
      case 98:
         return true;
      case 14:
      case 15:
      case 16:
      case 17:
      case 18:
      case 19:
      case 20:
      case 21:
      case 22:
      case 23:
      case 24:
      case 25:
      case 26:
      case 27:
      case 28:
      case 29:
      case 30:
      case 31:
      case 32:
      case 33:
      case 34:
      case 35:
      case 36:
      case 37:
      case 38:
      case 39:
      case 40:
      case 41:
      case 42:
      case 43:
      case 44:
      case 45:
      case 46:
      case 47:
      case 48:
      case 49:
      case 50:
      case 51:
      case 52:
      case 53:
      case 54:
      case 55:
      case 56:
      case 57:
      case 58:
      case 59:
      case 60:
      case 61:
      case 62:
      case 63:
      case 64:
      case 65:
      case 66:
      case 67:
      case 68:
      case 69:
      case 70:
      case 71:
      case 72:
      case 73:
      case 74:
      case 75:
      case 76:
      case 77:
      case 78:
      case 79:
      case 80:
      case 81:
      case 82:
      case 83:
      case 84:
      case 85:
      case 86:
      default:
         return false;
      }
   }

   final boolean a(int[] var1) {
      int var2 = var1[0] - this.c[0];
      int var3 = var1[1] - this.c[1];
      int var4;
      if ((var4 = GloftMMN.a((long)(var2 * var2 + var3 * var3))) > 0) {
         var2 = (var2 << 8) / var4;
         var3 = (var3 << 8) / var4;
      }

      if (var4 <= 32) {
         this.c[0] = var1[0];
         this.c[1] = var1[1];
         return true;
      } else {
         int[] var5 = new int[]{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
         int var6 = this.c[0] + var2 * 60 * 2 / 1000;
         int var7 = this.c[1] + var3 * 60 * 2 / 1000;
         int var8 = var6 >> 8;
         int var9 = var7 >> 8;
         int var10 = this.c[0] >> 8;
         int var11 = this.c[1] >> 8;
         int var12 = var8 - var10;
         int var13 = var9 - var11;
         var5[0] = var8;
         var5[1] = var9;
         if (var12 != 0 && var13 != 0) {
            var5[5] = var10 + var12;
            var5[6] = var11;
            var5[10] = var10;
            var5[11] = var11 + var13;
         } else {
            var5[5] = var10 - var13;
            var5[6] = var11 + var12;
            var5[10] = var10 + var13;
            var5[11] = var11 - var12;
         }

         int var14;
         int var15;
         int var16;
         int var17;
         for(var14 = 0; var14 < 3; ++var14) {
            var15 = var14 * 5;
            var16 = (var5[var15 + 0] << 8) + 127;
            var17 = (var5[var15 + 1] << 8) + 127;
            if (var14 == 0) {
               var16 = var6;
               var17 = var7;
            }

            int var18;
            boolean var19 = (var18 = f.b(var16, var17)) != 1;
            boolean var20 = !f.a(var18, var16, var17, this.b[6], this == h.s[0]);
            byte var21 = 0;
            if (var19 && var20) {
               var21 = 1;
            }

            var5[var15 + 2] = var21;
            var5[var15 + 3] = f.a(var10, var11, var5[var15 + 0], var5[var15 + 1]);
            var5[var15 + 4] = f.a(var1[0] >> 8, var1[1] >> 8, var5[var15 + 0], var5[var15 + 1]);
         }

         if (var5[2] != 1) {
            var16 = -1;
            var17 = 100000;

            for(var14 = 0; var14 < 3; ++var14) {
               var15 = var14 * 5;
               if (var5[var15 + 2] == 1 && var5[var15 + 4] < var17) {
                  var17 = var5[var15 + 4];
                  var16 = var14;
               }
            }

            if (var16 != -1) {
               var15 = var16 * 5;
               var2 = var5[var15 + 0] - var10 << 8;
               var3 = var5[var15 + 1] - var11 << 8;
               var4 = GloftMMN.a((long)(var2 * var2 + var3 * var3));
               var2 = (var2 << 8) / var4;
               var3 = (var3 << 8) / var4;
            }
         }

         this.c[0] += var2 * 60 * 2 / 1000;
         this.c[1] += var3 * 60 * 2 / 1000;
         f.a((g)this, var2, var3, 6, 4);
         return false;
      }
   }

   final boolean a(int var1, int var2, int var3, int var4, boolean var5) {
      d var6;
      boolean var7;
      if (var7 = (var6 = h.n[this.b[5] & 255]) == h.n[0]) {
         var3 += v;
      }

      if (var3 != -1) {
         var6.a(var3);
      }

      if (var4 != -1) {
         var6.c = var4;
      }

      boolean var8 = false;
      boolean var9 = false;
      int var13;
      int var14;
      if (var5) {
         var13 = var1;
         var14 = var2;
         var1 -= this.c[0];
         var2 -= this.c[1];
      } else {
         int var10;
         if ((var10 = (int)h.aH) > 200) {
            var10 = 200;
         }

         var13 = this.c[0] + var1 * var10 / 1000;
         var14 = this.c[1] + var2 * var10 / 1000;
      }

      boolean var15 = false;
      boolean var11 = false;
      if (var7) {
         int[] var12 = new int[]{0, 0};
         f.a(var13, var14, var12);
         if (var2 < 0 || var1 > 0) {
            var12[0] += 14;
         }

         if (var2 > 0 || var1 < 0) {
            var12[0] -= 14;
         }

         if (f.a(var12) == -1) {
            var15 = true;
         }
      }

      h.aR = var15;
      if (!var15) {
         if (!f.a(var13, var14, 127)) {
            this.c[0] = var13;
            this.c[1] = var14;
            var11 = true;
         } else if (!f.a(var13, this.c[1], 127)) {
            this.c[0] = var13;
            var11 = true;
         } else if (!f.a(this.c[0], var14, 127)) {
            this.c[1] = var14;
            var11 = true;
         }
      }

      this.o[0] = var1;
      this.o[1] = var2;
      f.c(this.o);
      this.n[0] = var1 * f.w[0] + var2 * f.x[0] >> 8;
      this.n[1] = var1 * f.w[1] + var2 * f.x[1] >> 8;
      f.c(this.n);
      if (var7) {
         p = 0L;
      }

      return var11;
   }

   static void c(Graphics var0) {
      if (N.size() > 0) {
         h.z = 17;
      } else {
         if (h.m >= 0) {
            h.z = 18;
            h.L = 0;
            h.H = h.m;
            h.m = -1;
         } else if (f.av >= 0 && !au && !h.O) {
            f.a(var0, true);
         } else if (O != 0) {
            h.a(O);
            h.y = P;
            P = 0;
            h.K = 0;
            h.z = -1;
         }

         au = false;
         O = 0;
      }
   }

   static void p() {
      if (f.ax != -1) {
         int var1 = f.g(f.i(f.ax, 5), -1);
         f.ap[var1] = f.aT;
      }

      h.n[0].a(0);
      f.ax = -1;
      f.i();
      h.z = 0;
      h.N[3] = 0;
      f.c();
   }

   static {
      byte[][] var10000 = new byte[][]{{21}, {7, 10}, {0, 1}, {7, 10, 3}};
      h = new byte[]{0, 0, 0, 6, 0, 0, 0, 5, 5, 0, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 2, 5, 2, 2, 1, 3, 3, 1, 6, 5, 6, 6, 4, 4, 2, 2, 2, 2, 2};
      i = new int[]{0, 60, 70, 80, 90, 100, 110, 60, 0, 70, 80, 90, 100, 110, 70, 60, 0, 80, 70, 60, 80, 80, 90, 100, 0, 60, 80, 90, 80, 80, 70, 60, 0, 70, 80, 100, 90, 60, 80, 60, 0, 60, 110, 90, 70, 110, 90, 60, 0};
      u = -1;
      v = 0;
      w = 0;
      B = new byte[]{13, 16, 17, 19, 20, 30};
      G = new int[25];
      H = -1;
      I = new int[168];
      N = new Vector();
      Q = 0;
      U = new Image[4];
      X = new byte[]{1, 3, 4};
      Y = new byte[]{1, 3, 6, 7, 8, 12};
      ag = new long[16];
      ah = new byte[39];
      ai = new boolean[39];
      aj = new short[][]{{13}, {1, 1, 505}, {10, 10, 506}, {25, 25, 508}, {45, 45, 510, 756}, {60, 60, 512}, {85, 85, 514}, {16}, {1, 1, 516}, {10, 10, 517}, {25, 25, 519}, {35, 35, 521, 757}, {60, 60, 523}, {80, 80, 525}, {17}, {10, 10, 528}, {25, 25, 530}, {45, 45, 532}, {60, 60, 534}, {90, 90, 536}, {19}, {10, 10, 539}, {20, 20, 541}, {40, 40, 543}, {61, 61, 545}, {85, 85, 547}, {20}, {10, 10, 551}, {20, 20, 549}, {25, 25, 553}, {40, 40, 555}, {60, 60, 557}, {95, 95, 559}, {30}, {1, 1, 572}, {10, 10, 573}, {20, 20, 575}, {35, 35, 577, 759}, {45, 45, 579}, {70, 70, 581}, {95, 95, 583}};
      am = new byte[]{12, 12, 12, 18, 1, 13, 1, 1, 1, 11, 8, 18, 1, 1, 8, 1, 1, 12, 1, 8, 1, 4, 11, 13, -1, 10, 5, -1, 18, 15, 18, 18, 6, -1, 11, 11, 9, 11, 11};
      aq = new short[16];
   }
}
